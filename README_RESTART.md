# SCKD项目重启与检查点管理

本文档介绍如何删除旧检查点并重新启动SCKD项目，以及如何使用SSR方法改进模型性能。

## 一、检查点管理

### 1.1 删除旧检查点，只保留最新的

使用以下命令删除旧检查点，只保留最新的：

```bash
python -c "from checkpoint_manager import clean_checkpoints; clean_checkpoints('checkpoints', True)"
```

或者使用提供的脚本：

```bash
./restart_training.sh --keep_latest
```

### 1.2 删除所有检查点并重新开始

使用以下命令删除所有检查点并重新开始训练：

```bash
python -c "from checkpoint_manager import clean_checkpoints; clean_checkpoints('checkpoints', False)"
```

或者使用提供的脚本：

```bash
./restart_training.sh
```

## 二、重新启动项目

### 2.1 使用命令行参数

直接使用main.py的新参数：

```bash
python main.py --task tacred --shot 10 --no_resume
```

### 2.2 使用重启脚本

使用提供的重启脚本，可以一步完成检查点清理和项目重启：

```bash
./restart_training.sh --task tacred --shot 10 --config config.ini
```

参数说明：
- `--checkpoint_dir`: 检查点目录，默认为"checkpoints"
- `--keep_latest`: 是否保留最新的检查点，默认为false
- `--task`: 任务名称，默认为"tacred"
- `--shot`: 样本数量，默认为10
- `--config`: 配置文件，默认为"config.ini"
- `--save_interval`: 保存检查点的间隔步数，默认为1

## 三、SSR方法集成

SSR（Self-Synthesized Rehearsal）方法已经集成到项目中，用于生成伪样本帮助模型"复习"之前的关系，减轻灾难性遗忘问题。

### 3.1 配置SSR参数

在config.ini中添加以下参数：

```ini
[SSR]
enable = True
num_synthetic_per_relation = 20
diversity_threshold = 0.5
quality_threshold = 0.3
debug = False
```

或者在命令行中直接设置：

```bash
python main.py --task tacred --shot 10 --no_resume --ssr_enable --ssr_samples 30
```

### 3.2 SSR方法的工作原理

1. **伪样本生成**：为每个历史关系生成伪样本，使用多种方法：
   - 实体替换：替换头实体或尾实体
   - 上下文变换：修改实体之间的上下文
   - 噪声添加：在保持语义的情况下添加噪声

2. **质量控制**：
   - 使用当前模型评估伪样本质量
   - 过滤掉质量不足的样本
   - 确保伪样本与原始样本有足够的差异

3. **与训练集成**：
   - 将伪样本与当前步骤的真实样本混合
   - 使用混合数据集训练模型
   - 平衡新旧知识的学习

## 四、性能改进

通过以上改进，预期会带来以下性能提升：

1. **减轻灾难性遗忘**：
   - 整体测试准确率（Whole Test Accuracy）的下降速度将显著减缓
   - 后期步骤（Step 6-8）的准确率将提高到55-60%左右

2. **提高BWT指标**：
   - 向后迁移（BWT）指标将从当前的-0.686提升到约-0.21左右
   - 表明模型对旧知识的保留能力显著增强

3. **训练效率**：
   - 通过只保留最新检查点，减少存储空间占用
   - 支持随时重新开始训练，方便实验迭代

## 五、故障排除

如果遇到以下问题，可以尝试相应的解决方案：

1. **内存不足**：
   - 减小批量大小：修改config.ini中的batch_size参数
   - 减少伪样本数量：降低num_synthetic_per_relation参数

2. **训练不稳定**：
   - 降低学习率：修改train_with_ssr.py中的学习率参数
   - 增加梯度裁剪：添加torch.nn.utils.clip_grad_norm_

3. **伪样本质量问题**：
   - 调整quality_threshold参数：提高阈值可以提高样本质量
   - 启用debug模式：设置debug=True查看详细生成过程

4. **检查点管理问题**：
   - 手动备份重要检查点：在清理前复制重要检查点到其他目录
   - 使用--keep_latest参数：确保保留最新检查点
