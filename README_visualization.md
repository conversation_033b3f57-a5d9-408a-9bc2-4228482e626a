# SCKD实验结果可视化工具

本工具集用于从SCKD项目的检查点中提取实验结果数据，并生成各种可视化图表，帮助分析和理解实验结果。

## 功能概述

1. **提取结果数据**：从检查点文件中提取实验结果数据，包括准确率、BWT、FWT等指标。
2. **更新检查点**：将提取的结果数据添加到检查点文件中，方便后续分析。
3. **基本可视化**：生成基本的性能图表，包括整体性能、每轮性能、准确率热力图和遗忘分析。
4. **高级可视化**：生成更多类型的图表，包括学习曲线、轮次比较、BWT热力图等。
5. **摘要报告**：生成实验结果的摘要统计报告。

## 文件说明

- `extract_results.py`：从检查点提取结果数据，并更新检查点文件。
- `visualize_results.py`：基本可视化工具，生成基本的性能图表。
- `advanced_visualization.py`：高级可视化工具，生成更多类型的图表和摘要报告。
- `visualize_experiment.sh`：一键执行所有可视化步骤的脚本。

## 使用方法

### 一键可视化

最简单的方法是使用一键可视化脚本：

```bash
# 添加执行权限
chmod +x visualize_experiment.sh

# 运行脚本
./visualize_experiment.sh
```

这将执行以下步骤：
1. 更新检查点文件，添加结果数据
2. 从检查点提取实验结果数据
3. 生成基本可视化图表
4. 生成高级可视化图表和摘要报告

### 单独使用各工具

如果需要单独使用各工具，可以按照以下方式执行：

#### 1. 提取结果数据

```bash
# 更新检查点文件，添加结果数据
python extract_results.py --update

# 从检查点提取结果数据到指定文件
python extract_results.py --output_file results.json
```

#### 2. 基本可视化

```bash
python visualize_results.py --checkpoint_dir checkpoints --output_dir visualizations
```

#### 3. 高级可视化

```bash
python advanced_visualization.py --results_file visualizations/results.json --output_dir visualizations
```

## 生成的图表

### 基本图表

- `overall_performance.png`：整体性能图表，显示准确率和迁移指标随训练进度的变化。
- `per_round_performance.png`：每轮性能图表，显示不同轮次的性能变化。
- `accuracy_heatmap.png`：准确率热力图，显示不同轮次和步骤的准确率分布。
- `forgetting_analysis.png`：遗忘分析图，显示BWT指标随轮次的变化。

### 高级图表

- `learning_curve.png`：学习曲线，显示整体测试准确率、当前测试准确率、BWT和FWT随训练进度的变化。
- `round_comparison.png`：轮次比较图，比较不同轮次的整体测试准确率。
- `whole_acc_heatmap.png`：整体准确率热力图，显示不同轮次和步骤的整体测试准确率分布。
- `bwt_heatmap.png`：BWT热力图，显示不同轮次和步骤的BWT指标分布。

### 摘要报告

- `summary_report.json`：JSON格式的摘要统计报告，包含各指标的均值、标准差、最小值和最大值。
- `summary_report.csv`：CSV格式的摘要统计报告，方便导入到电子表格软件中分析。

## 指标说明

- **整体测试准确率（Whole Test Accuracy）**：模型在所有已学习关系上的表现。
- **当前测试准确率（Current Test Accuracy）**：模型在新关系上的表现。
- **BWT（Backward Transfer）**：学习新关系后，对旧关系识别能力的影响。正值表示积极影响，负值表示遗忘。
- **FWT（Forward Transfer）**：学习旧关系后，对新关系识别能力的影响。正值表示积极影响。

## 依赖库

- Python 3.8+
- PyTorch
- NumPy
- Matplotlib
- Seaborn
- Pandas

## 注意事项

1. 确保检查点文件包含必要的结果数据，如`result_whole_test`、`result_cur_test`、`bwt_whole`和`fwt_whole`。
2. 如果检查点文件不包含这些数据，可以使用`extract_results.py --update`命令更新检查点文件。
3. 可视化结果保存在`visualizations`目录中，可以根据需要修改输出目录。
