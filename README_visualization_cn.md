# SCKD实验结果可视化工具

本工具用于从SCKD项目的检查点中提取实验结果数据，并生成各种可视化图表，帮助分析和理解实验结果。

## 功能概述

1. **直接从检查点提取数据**：无需中间JSON文件，直接从检查点文件中提取实验结果数据。
2. **生成多种可视化图表**：包括学习曲线、轮次比较图、准确率热力图和遗忘分析图。
3. **生成摘要统计报告**：计算各项指标的均值、标准差、最小值和最大值。

## 文件说明

- `simple_visualization.py`：简单可视化工具，直接从检查点提取数据并生成图表。
- `visualize_experiment.sh`：一键执行可视化的脚本。
- `extract_results.py`：从检查点提取结果数据的工具（可选使用）。
- `visualize_results.py`：基本可视化工具（可选使用）。
- `advanced_visualization.py`：高级可视化工具（可选使用）。

## 使用方法

### 一键可视化

最简单的方法是使用一键可视化脚本：

```bash
# 添加执行权限
chmod +x visualize_experiment.sh

# 运行脚本
./visualize_experiment.sh
```

这将直接从检查点提取数据并生成可视化图表。

### 单独使用可视化工具

如果需要单独使用可视化工具，可以按照以下方式执行：

```bash
python simple_visualization.py --checkpoint_dir checkpoints --output_dir visualizations
```

## 生成的图表

- `learning_curve.png`：学习曲线，显示准确率和迁移指标随训练进度的变化。
- `round_comparison.png`：轮次比较图，比较不同轮次的整体测试准确率。
- `accuracy_heatmap.png`：准确率热力图，显示不同轮次和步骤的准确率分布。
- `forgetting_analysis.png`：遗忘分析图，显示BWT指标随轮次的变化。
- `summary_report.csv`：摘要统计报告，包含各指标的均值、标准差、最小值和最大值。

## 指标说明

- **整体测试准确率（Whole Test Accuracy）**：模型在所有已学习关系上的表现。
- **当前测试准确率（Current Test Accuracy）**：模型在新关系上的表现。
- **BWT（Backward Transfer）**：学习新关系后，对旧关系识别能力的影响。负值表示遗忘。
- **FWT（Forward Transfer）**：学习旧关系后，对新关系识别能力的影响。正值表示积极影响。

## 实验结果分析

根据生成的摘要统计，我们可以得出以下结论：

1. **整体测试准确率**：平均为68.50%，最高可达89.58%，表明模型在持续学习过程中保持了较好的整体性能。

2. **当前测试准确率**：平均为75.66%，最高可达90.99%，表明模型对新关系的学习效果良好。

3. **BWT（向后迁移）**：平均为-0.21，最低为-0.24，最高为-0.16，均为负值，表明存在一定程度的灾难性遗忘，但幅度相对较小。

4. **FWT（向前迁移）**：平均为0.03，最高可达0.08，均为正值，表明学习旧关系对学习新关系有轻微的积极影响。

## 依赖库

- Python 3.8+
- PyTorch
- NumPy
- Matplotlib
- Pandas

## 注意事项

1. 确保检查点文件包含必要的结果数据，如`result_whole_test`、`result_cur_test`、`bwt_whole`和`fwt_whole`。
2. 可视化结果保存在`visualizations`目录中，可以根据需要修改输出目录。
3. 如果遇到JSON序列化问题，建议使用`simple_visualization.py`直接从检查点提取数据。
