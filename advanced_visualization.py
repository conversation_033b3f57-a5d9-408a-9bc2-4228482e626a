#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import argparse
import pandas as pd
from matplotlib.ticker import MaxNLocator
from matplotlib.gridspec import GridSpec

def load_results(results_file):
    """加载结果数据"""
    with open(results_file, 'r') as f:
        results = json.load(f)
    return results

def create_dataframe(results):
    """创建数据框"""
    data = []
    for checkpoint in results['checkpoints']:
        row = {
            'task': checkpoint['task'],
            'shot': checkpoint['shot'],
            'round': checkpoint['round'],
            'step': checkpoint['step'],
            'timestamp': checkpoint.get('timestamp', None)
        }
        
        # 添加性能指标
        if 'result_whole_test' in checkpoint:
            whole_test = checkpoint['result_whole_test']
            if isinstance(whole_test, list) and len(whole_test) > 0:
                if isinstance(whole_test[-1], list) and len(whole_test[-1]) > checkpoint['step'] - 1:
                    row['whole_test_acc'] = whole_test[-1][checkpoint['step'] - 1]
        
        if 'result_cur_test' in checkpoint:
            cur_test = checkpoint['result_cur_test']
            if isinstance(cur_test, list) and len(cur_test) > 0:
                if isinstance(cur_test[-1], list) and len(cur_test[-1]) > checkpoint['step'] - 1:
                    row['cur_test_acc'] = cur_test[-1][checkpoint['step'] - 1]
        
        if 'avg_result_cur_test' in checkpoint:
            avg_cur_test = checkpoint['avg_result_cur_test']
            if isinstance(avg_cur_test, list) and len(avg_cur_test) > checkpoint['step'] - 1:
                row['avg_cur_test_acc'] = avg_cur_test[checkpoint['step'] - 1]
        
        if 'avg_result_all_test' in checkpoint:
            avg_all_test = checkpoint['avg_result_all_test']
            if isinstance(avg_all_test, list) and len(avg_all_test) > checkpoint['step'] - 1:
                row['avg_all_test_acc'] = avg_all_test[checkpoint['step'] - 1]
        
        if 'std_result_all_test' in checkpoint:
            std_all_test = checkpoint['std_result_all_test']
            if isinstance(std_all_test, list) and len(std_all_test) > checkpoint['step'] - 1:
                row['std_all_test_acc'] = std_all_test[checkpoint['step'] - 1]
        
        if 'bwt_whole' in checkpoint:
            bwt = checkpoint['bwt_whole']
            if isinstance(bwt, list) and len(bwt) > 0:
                row['bwt'] = bwt[-1]
        
        if 'fwt_whole' in checkpoint:
            fwt = checkpoint['fwt_whole']
            if isinstance(fwt, list) and len(fwt) > 0:
                row['fwt'] = fwt[-1]
        
        data.append(row)
    
    return pd.DataFrame(data)

def plot_learning_curve(df, output_dir):
    """绘制学习曲线"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 设置图表样式
    sns.set(style="whitegrid", font_scale=1.2)
    plt.figure(figsize=(14, 10))
    
    # 创建网格布局
    gs = GridSpec(2, 2, figure=plt.gcf())
    
    # 绘制整体测试准确率
    ax1 = plt.subplot(gs[0, 0])
    df['position'] = df['round'] + df['step'] / 10
    sns.lineplot(x='position', y='whole_test_acc', data=df, marker='o', ax=ax1)
    ax1.set_title('Whole Test Accuracy')
    ax1.set_xlabel('Training Progress (Round.Step)')
    ax1.set_ylabel('Accuracy (%)')
    ax1.grid(True)
    
    # 绘制当前测试准确率
    ax2 = plt.subplot(gs[0, 1])
    sns.lineplot(x='position', y='cur_test_acc', data=df, marker='o', ax=ax2)
    ax2.set_title('Current Test Accuracy')
    ax2.set_xlabel('Training Progress (Round.Step)')
    ax2.set_ylabel('Accuracy (%)')
    ax2.grid(True)
    
    # 绘制BWT和FWT
    ax3 = plt.subplot(gs[1, :])
    sns.lineplot(x='position', y='bwt', data=df, marker='o', label='BWT', ax=ax3)
    sns.lineplot(x='position', y='fwt', data=df, marker='o', label='FWT', ax=ax3)
    ax3.set_title('Transfer Metrics')
    ax3.set_xlabel('Training Progress (Round.Step)')
    ax3.set_ylabel('Transfer Score')
    ax3.legend()
    ax3.grid(True)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'learning_curve.png'), dpi=300)
    plt.close()

def plot_round_comparison(df, output_dir):
    """比较不同轮次的性能"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 设置图表样式
    sns.set(style="whitegrid", font_scale=1.2)
    plt.figure(figsize=(14, 8))
    
    # 按轮次分组
    rounds = sorted(df['round'].unique())
    
    # 绘制每轮的整体测试准确率
    for r in rounds:
        round_data = df[df['round'] == r]
        plt.plot(round_data['step'], round_data['whole_test_acc'], 
                 marker='o', label=f'Round {r}')
    
    plt.title('Whole Test Accuracy by Round')
    plt.xlabel('Step')
    plt.ylabel('Accuracy (%)')
    plt.legend()
    plt.grid(True)
    
    # 设置x轴刻度为整数
    ax = plt.gca()
    ax.xaxis.set_major_locator(MaxNLocator(integer=True))
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'round_comparison.png'), dpi=300)
    plt.close()

def plot_forgetting_analysis(df, output_dir):
    """分析遗忘现象"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 设置图表样式
    sns.set(style="whitegrid", font_scale=1.2)
    plt.figure(figsize=(14, 8))
    
    # 创建网格布局
    gs = GridSpec(1, 2, figure=plt.gcf())
    
    # 绘制BWT随轮次变化
    ax1 = plt.subplot(gs[0, 0])
    sns.boxplot(x='round', y='bwt', data=df, ax=ax1)
    ax1.set_title('Backward Transfer (BWT) by Round')
    ax1.set_xlabel('Round')
    ax1.set_ylabel('BWT Score')
    ax1.grid(True)
    
    # 绘制BWT随步骤变化
    ax2 = plt.subplot(gs[0, 1])
    sns.boxplot(x='step', y='bwt', data=df, ax=ax2)
    ax2.set_title('Backward Transfer (BWT) by Step')
    ax2.set_xlabel('Step')
    ax2.set_ylabel('BWT Score')
    ax2.grid(True)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'forgetting_analysis.png'), dpi=300)
    plt.close()

def plot_performance_heatmap(df, output_dir):
    """绘制性能热力图"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 设置图表样式
    sns.set(style="whitegrid", font_scale=1.2)
    
    # 创建轮次-步骤网格
    pivot_whole = df.pivot_table(index='round', columns='step', 
                                values='whole_test_acc', aggfunc='mean')
    
    # 绘制整体测试准确率热力图
    plt.figure(figsize=(12, 8))
    sns.heatmap(pivot_whole, annot=True, cmap='YlGnBu', fmt='.2f', linewidths=.5)
    plt.title('Whole Test Accuracy Heatmap (Round vs Step)')
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'whole_acc_heatmap.png'), dpi=300)
    plt.close()
    
    # 创建BWT热力图
    pivot_bwt = df.pivot_table(index='round', columns='step', 
                              values='bwt', aggfunc='mean')
    
    plt.figure(figsize=(12, 8))
    sns.heatmap(pivot_bwt, annot=True, cmap='RdBu', fmt='.2f', linewidths=.5, center=0)
    plt.title('Backward Transfer (BWT) Heatmap (Round vs Step)')
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'bwt_heatmap.png'), dpi=300)
    plt.close()

def create_summary_report(df, output_dir):
    """创建摘要报告"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 计算整体统计数据
    summary = {
        'whole_test_acc': {
            'mean': df['whole_test_acc'].mean(),
            'std': df['whole_test_acc'].std(),
            'min': df['whole_test_acc'].min(),
            'max': df['whole_test_acc'].max()
        },
        'cur_test_acc': {
            'mean': df['cur_test_acc'].mean(),
            'std': df['cur_test_acc'].std(),
            'min': df['cur_test_acc'].min(),
            'max': df['cur_test_acc'].max()
        },
        'bwt': {
            'mean': df['bwt'].mean(),
            'std': df['bwt'].std(),
            'min': df['bwt'].min(),
            'max': df['bwt'].max()
        },
        'fwt': {
            'mean': df['fwt'].mean(),
            'std': df['fwt'].std(),
            'min': df['fwt'].min(),
            'max': df['fwt'].max()
        }
    }
    
    # 保存摘要报告
    with open(os.path.join(output_dir, 'summary_report.json'), 'w') as f:
        json.dump(summary, f, indent=4)
    
    # 创建摘要表格
    summary_df = pd.DataFrame({
        'Metric': ['Whole Test Acc', 'Current Test Acc', 'BWT', 'FWT'],
        'Mean': [summary['whole_test_acc']['mean'], summary['cur_test_acc']['mean'], 
                summary['bwt']['mean'], summary['fwt']['mean']],
        'Std': [summary['whole_test_acc']['std'], summary['cur_test_acc']['std'], 
               summary['bwt']['std'], summary['fwt']['std']],
        'Min': [summary['whole_test_acc']['min'], summary['cur_test_acc']['min'], 
               summary['bwt']['min'], summary['fwt']['min']],
        'Max': [summary['whole_test_acc']['max'], summary['cur_test_acc']['max'], 
               summary['bwt']['max'], summary['fwt']['max']]
    })
    
    # 保存摘要表格为CSV
    summary_df.to_csv(os.path.join(output_dir, 'summary_report.csv'), index=False)

def main():
    parser = argparse.ArgumentParser(description='高级可视化SCKD实验结果')
    parser.add_argument('--results_file', type=str, default='visualizations/results.json',
                        help='结果数据文件路径')
    parser.add_argument('--output_dir', type=str, default='visualizations',
                        help='可视化输出目录路径')
    args = parser.parse_args()
    
    print(f"从 {args.results_file} 加载结果数据...")
    results = load_results(args.results_file)
    
    print("创建数据框...")
    df = create_dataframe(results)
    
    if df.empty:
        print("没有找到有效的结果数据。请确保结果文件包含正确的数据格式。")
        return
    
    print(f"生成可视化结果到 {args.output_dir}...")
    plot_learning_curve(df, args.output_dir)
    plot_round_comparison(df, args.output_dir)
    plot_forgetting_analysis(df, args.output_dir)
    plot_performance_heatmap(df, args.output_dir)
    create_summary_report(df, args.output_dir)
    
    print("高级可视化完成！")

if __name__ == "__main__":
    main()
