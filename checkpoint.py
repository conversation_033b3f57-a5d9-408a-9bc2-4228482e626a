import os
import torch
import json
import time

def save_checkpoint(config, state, checkpoint_dir='checkpoints', filename=None):
    """
    保存检查点
    
    Args:
        config: 配置对象
        state: 包含模型状态、优化器状态和训练进度的字典
        checkpoint_dir: 检查点保存目录
        filename: 检查点文件名，如果为None则自动生成
    """
    # 创建检查点目录
    if not os.path.exists(checkpoint_dir):
        os.makedirs(checkpoint_dir)
    
    # 如果没有指定文件名，则自动生成一个基于时间的文件名
    if filename is None:
        timestamp = time.strftime('%Y%m%d_%H%M%S', time.localtime())
        task_info = f"{config.task}_{config.shot}"
        round_step = f"round{state['round']}_step{state['step']}"
        filename = f"checkpoint_{task_info}_{round_step}_{timestamp}.pt"
    
    # 完整的检查点路径
    checkpoint_path = os.path.join(checkpoint_dir, filename)
    
    # 保存检查点
    torch.save(state, checkpoint_path)
    
    # 保存最新检查点的信息
    latest_info = {
        'latest_checkpoint': filename,
        'timestamp': time.time(),
        'task': config.task,
        'shot': config.shot,
        'round': state['round'],
        'step': state['step']
    }
    
    with open(os.path.join(checkpoint_dir, 'latest_checkpoint.json'), 'w') as f:
        json.dump(latest_info, f, indent=4)
    
    print(f"检查点已保存到 {checkpoint_path}")
    return checkpoint_path

def load_checkpoint(checkpoint_path, encoder, dropout_layer, classifier=None, optimizer=None):
    """
    加载检查点
    
    Args:
        checkpoint_path: 检查点文件路径
        encoder: 编码器模型
        dropout_layer: Dropout层
        classifier: 分类器模型（可选）
        optimizer: 优化器（可选）
    
    Returns:
        包含训练状态的字典
    """
    if not os.path.exists(checkpoint_path):
        raise FileNotFoundError(f"检查点文件 {checkpoint_path} 不存在")
    
    # 加载检查点
    checkpoint = torch.load(checkpoint_path, map_location=lambda storage, loc: storage)
    
    # 加载模型状态
    encoder.load_state_dict(checkpoint['encoder_state_dict'])
    dropout_layer.load_state_dict(checkpoint['dropout_layer_state_dict'])
    
    if classifier is not None and 'classifier_state_dict' in checkpoint:
        classifier.load_state_dict(checkpoint['classifier_state_dict'])
    
    if optimizer is not None and 'optimizer_state_dict' in checkpoint:
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
    
    print(f"检查点已从 {checkpoint_path} 加载")
    return checkpoint

def get_latest_checkpoint(checkpoint_dir='checkpoints'):
    """
    获取最新的检查点信息
    
    Args:
        checkpoint_dir: 检查点目录
    
    Returns:
        最新检查点的路径，如果没有检查点则返回None
    """
    latest_info_path = os.path.join(checkpoint_dir, 'latest_checkpoint.json')
    
    if not os.path.exists(latest_info_path):
        return None
    
    with open(latest_info_path, 'r') as f:
        latest_info = json.load(f)
    
    latest_checkpoint = latest_info.get('latest_checkpoint')
    if latest_checkpoint:
        checkpoint_path = os.path.join(checkpoint_dir, latest_checkpoint)
        if os.path.exists(checkpoint_path):
            return checkpoint_path
    
    return None

def create_checkpoint_state(config, encoder, dropout_layer, classifier, optimizer, 
                           round_num, step_num, epoch_num=None, 
                           history_relations=None, memorized_samples=None, 
                           relation_standard=None, additional_data=None):
    """
    创建检查点状态字典
    
    Args:
        config: 配置对象
        encoder: 编码器模型
        dropout_layer: Dropout层
        classifier: 分类器模型
        optimizer: 优化器
        round_num: 当前轮次
        step_num: 当前步骤
        epoch_num: 当前epoch（可选）
        history_relations: 历史关系列表（可选）
        memorized_samples: 记忆的样本（可选）
        relation_standard: 关系标准（可选）
        additional_data: 其他需要保存的数据（可选）
    
    Returns:
        包含所有状态的字典
    """
    state = {
        'round': round_num,
        'step': step_num,
        'epoch': epoch_num,
        'encoder_state_dict': encoder.state_dict(),
        'dropout_layer_state_dict': dropout_layer.state_dict(),
        'classifier_state_dict': classifier.state_dict() if classifier else None,
        'optimizer_state_dict': optimizer.state_dict() if optimizer else None,
        'config': {
            'task': config.task,
            'shot': config.shot,
            'device': str(config.device),
            'seed': config.seed
        },
        'timestamp': time.time()
    }
    
    # 添加可选数据
    if history_relations:
        state['history_relations'] = history_relations
    
    if memorized_samples:
        state['memorized_samples'] = memorized_samples
    
    if relation_standard:
        state['relation_standard'] = relation_standard
    
    if additional_data:
        state.update(additional_data)
    
    return state
