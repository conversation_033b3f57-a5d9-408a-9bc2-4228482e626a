import os
import glob
import re
import shutil
import torch

def get_checkpoint_files(checkpoint_dir):
    """获取检查点目录中的所有检查点文件"""
    # 支持多种可能的检查点文件模式
    checkpoint_patterns = [
        os.path.join(checkpoint_dir, "checkpoint_*.pt"),
        os.path.join(checkpoint_dir, "checkpoint_*.pth"),
        os.path.join(checkpoint_dir, "*.pt"),
        os.path.join(checkpoint_dir, "*.pth"),
        os.path.join(checkpoint_dir, "checkpoint_*")
    ]
    
    all_checkpoints = []
    for pattern in checkpoint_patterns:
        all_checkpoints.extend(glob.glob(pattern))
    
    return all_checkpoints

def get_latest_checkpoint(checkpoint_dir):
    """获取最新的检查点文件"""
    checkpoint_files = get_checkpoint_files(checkpoint_dir)
    
    if not checkpoint_files:
        return None
    
    # 按文件修改时间排序
    checkpoint_files.sort(key=os.path.getmtime, reverse=True)
    return checkpoint_files[0]

def clean_checkpoints(checkpoint_dir, keep_latest=True):
    """清理检查点目录，只保留最新的检查点（如果keep_latest为True）"""
    checkpoint_files = get_checkpoint_files(checkpoint_dir)
    
    if not checkpoint_files:
        print(f"检查点目录 {checkpoint_dir} 中没有找到检查点文件")
        return
    
    # 按文件修改时间排序
    checkpoint_files.sort(key=os.path.getmtime, reverse=True)
    
    # 保留最新的检查点
    if keep_latest and checkpoint_files:
        latest_checkpoint = checkpoint_files[0]
        checkpoint_files = checkpoint_files[1:]
        print(f"保留最新检查点: {os.path.basename(latest_checkpoint)}")
    
    # 删除其他检查点
    for checkpoint_file in checkpoint_files:
        try:
            os.remove(checkpoint_file)
            print(f"已删除检查点: {os.path.basename(checkpoint_file)}")
        except Exception as e:
            print(f"删除检查点 {os.path.basename(checkpoint_file)} 时出错: {e}")
    
    print(f"检查点清理完成，共删除 {len(checkpoint_files)} 个检查点文件")

def save_checkpoint(checkpoint_dir, state, filename=None):
    """保存检查点，并可选择性地清理旧检查点"""
    if not os.path.exists(checkpoint_dir):
        os.makedirs(checkpoint_dir)
    
    if filename is None:
        # 生成检查点文件名
        round_num = state.get('round', 0)
        step_num = state.get('step', 0)
        filename = f"checkpoint_round{round_num}_step{step_num}.pt"
    
    checkpoint_path = os.path.join(checkpoint_dir, filename)
    torch.save(state, checkpoint_path)
    print(f"检查点已保存到: {checkpoint_path}")
    
    return checkpoint_path

def reset_training_state():
    """重置训练状态，用于重新开始训练"""
    return {
        'round': 0,
        'step': 0,
        'encoder_state_dict': None,
        'dropout_layer_state_dict': None,
        'classifier_state_dict': None,
        'history_relations': [],
        'memorized_samples': {},
        'relation_standard': {},
        'test_cur': [],
        'test_total': [],
        'result_cur_test': [],
        'result_whole_test': [],
        'bwt_whole': [],
        'fwt_whole': [],
        'history_data': []
    }
