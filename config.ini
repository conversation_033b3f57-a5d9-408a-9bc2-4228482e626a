[training]
batch_size = 64
gradient_accumulation_steps = 4
total_round = 6
drop_out = 0.5
num_workers = 2
step1_epochs = 10
step2_epochs = 10
num_protos = 1
device = cuda
seed = 100
max_grad_norm = 10
task_length = 8
kl_temp = 2
temp = 0.1

[ssr]
# SSR (Self-Synthesized Rehearsal) 配置
num_synthetic_per_relation = 20  # 每个关系生成的伪样本数量
diversity_threshold = 0.7  # 伪样本多样性阈值
quality_threshold = 0.8  # 伪样本质量阈值
enable = true  # 是否启用SSR

[Encoder]
bert_path = /home/<USER>/fsy666/LLMs/bert-base-uncased
max_length = 256
vocab_size = 30522
marker_size = 4
pattern = entity_marker
encoder_output_size = 768


[dropout]
drop_p = 0.1
f_pass = 10
kappa_neg = 0.03
kappa_pos = 0.05


[scheduler]
T_mult = 1
rewarm_epoch_num = 2
# StepLR
decay_rate = 0.9
decay_steps = 800
