#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import torch
import json
import argparse
import numpy as np
from collections import defaultdict

def extract_results_from_checkpoints(checkpoint_dir='checkpoints', output_file='results.json'):
    """
    从检查点文件中提取实验结果数据

    Args:
        checkpoint_dir: 检查点目录
        output_file: 输出结果文件路径
    """
    results = {'checkpoints': []}

    # 获取所有检查点文件
    checkpoint_files = [f for f in os.listdir(checkpoint_dir) if f.endswith('.pt')]
    checkpoint_files.sort()  # 按文件名排序

    for checkpoint_file in checkpoint_files:
        # 解析文件名以获取轮次和步骤信息
        parts = checkpoint_file.split('_')
        task = parts[1]
        shot = int(parts[2])
        round_num = int(parts[3].replace('round', ''))
        step_num = int(parts[4].replace('step', ''))

        # 加载检查点
        checkpoint_path = os.path.join(checkpoint_dir, checkpoint_file)
        try:
            checkpoint = torch.load(checkpoint_path, map_location=lambda storage, loc: storage)

            # 创建结果数据结构
            result_data = {
                'task': task,
                'shot': shot,
                'round': round_num,
                'step': step_num,
                'timestamp': checkpoint.get('timestamp', None)
            }

            # 提取性能指标
            if 'result_cur_test' in checkpoint:
                if isinstance(checkpoint['result_cur_test'], np.ndarray):
                    result_data['result_cur_test'] = checkpoint['result_cur_test'].tolist()
                else:
                    result_data['result_cur_test'] = checkpoint['result_cur_test']

            if 'result_whole_test' in checkpoint:
                if isinstance(checkpoint['result_whole_test'], np.ndarray):
                    result_data['result_whole_test'] = checkpoint['result_whole_test'].tolist()
                else:
                    result_data['result_whole_test'] = checkpoint['result_whole_test']

            if 'avg_result_cur_test' in checkpoint:
                if isinstance(checkpoint['avg_result_cur_test'], np.ndarray):
                    result_data['avg_result_cur_test'] = checkpoint['avg_result_cur_test'].tolist()
                else:
                    result_data['avg_result_cur_test'] = checkpoint['avg_result_cur_test']

            if 'avg_result_all_test' in checkpoint:
                if isinstance(checkpoint['avg_result_all_test'], np.ndarray):
                    result_data['avg_result_all_test'] = checkpoint['avg_result_all_test'].tolist()
                else:
                    result_data['avg_result_all_test'] = checkpoint['avg_result_all_test']

            if 'std_result_all_test' in checkpoint:
                if isinstance(checkpoint['std_result_all_test'], np.ndarray):
                    result_data['std_result_all_test'] = checkpoint['std_result_all_test'].tolist()
                else:
                    result_data['std_result_all_test'] = checkpoint['std_result_all_test']

            if 'bwt_whole' in checkpoint:
                if isinstance(checkpoint['bwt_whole'], np.ndarray):
                    result_data['bwt_whole'] = checkpoint['bwt_whole'].tolist()
                else:
                    result_data['bwt_whole'] = checkpoint['bwt_whole']

            if 'fwt_whole' in checkpoint:
                if isinstance(checkpoint['fwt_whole'], np.ndarray):
                    result_data['fwt_whole'] = checkpoint['fwt_whole'].tolist()
                else:
                    result_data['fwt_whole'] = checkpoint['fwt_whole']

            # 添加到结果列表
            results['checkpoints'].append(result_data)

            print(f"已提取检查点 {checkpoint_file} 的结果数据")
        except Exception as e:
            print(f"处理检查点 {checkpoint_file} 时出错: {e}")

    # 保存结果到JSON文件
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=4)

    print(f"结果数据已保存到 {output_file}")
    return results

def update_checkpoints_with_results(checkpoint_dir='checkpoints', results_file=None):
    """
    更新检查点文件，添加实验结果数据

    Args:
        checkpoint_dir: 检查点目录
        results_file: 结果数据文件路径，如果为None则从检查点中提取
    """
    # 如果提供了结果文件，则加载结果数据
    results_data = None
    if results_file and os.path.exists(results_file):
        with open(results_file, 'r') as f:
            results_data = json.load(f)

    # 获取所有检查点文件
    checkpoint_files = [f for f in os.listdir(checkpoint_dir) if f.endswith('.pt')]
    checkpoint_files.sort()  # 按文件名排序

    for checkpoint_file in checkpoint_files:
        # 解析文件名以获取轮次和步骤信息
        parts = checkpoint_file.split('_')
        task = parts[1]
        shot = int(parts[2])
        round_num = int(parts[3].replace('round', ''))
        step_num = int(parts[4].replace('step', ''))

        # 加载检查点
        checkpoint_path = os.path.join(checkpoint_dir, checkpoint_file)
        try:
            checkpoint = torch.load(checkpoint_path, map_location=lambda storage, loc: storage)

            # 如果没有additional_data字段，则创建
            if 'additional_data' not in checkpoint:
                checkpoint['additional_data'] = {}

            # 如果没有results字段，则创建
            if 'results' not in checkpoint['additional_data']:
                checkpoint['additional_data']['results'] = {}

            # 添加结果数据
            results = checkpoint['additional_data']['results']

            # 从检查点中提取结果数据
            if 'result_cur_test' in checkpoint:
                results['result_cur_test'] = checkpoint['result_cur_test']

            if 'result_whole_test' in checkpoint:
                results['result_whole_test'] = checkpoint['result_whole_test']

            if 'avg_result_cur_test' in checkpoint:
                results['avg_result_cur_test'] = checkpoint['avg_result_cur_test'].tolist() if hasattr(checkpoint['avg_result_cur_test'], 'tolist') else checkpoint['avg_result_cur_test']

            if 'avg_result_all_test' in checkpoint:
                results['avg_result_all_test'] = checkpoint['avg_result_all_test'].tolist() if hasattr(checkpoint['avg_result_all_test'], 'tolist') else checkpoint['avg_result_all_test']

            if 'std_result_all_test' in checkpoint:
                results['std_result_all_test'] = checkpoint['std_result_all_test'].tolist() if hasattr(checkpoint['std_result_all_test'], 'tolist') else checkpoint['std_result_all_test']

            if 'bwt_whole' in checkpoint:
                results['bwt_whole'] = checkpoint['bwt_whole']

            if 'fwt_whole' in checkpoint:
                results['fwt_whole'] = checkpoint['fwt_whole']

            # 保存更新后的检查点
            torch.save(checkpoint, checkpoint_path)

            print(f"已更新检查点 {checkpoint_file} 的结果数据")
        except Exception as e:
            print(f"更新检查点 {checkpoint_file} 时出错: {e}")

def main():
    parser = argparse.ArgumentParser(description='从检查点提取实验结果数据')
    parser.add_argument('--checkpoint_dir', type=str, default='checkpoints',
                        help='检查点目录路径')
    parser.add_argument('--output_file', type=str, default='results.json',
                        help='输出结果文件路径')
    parser.add_argument('--update', action='store_true',
                        help='是否更新检查点文件，添加结果数据')
    args = parser.parse_args()

    if args.update:
        print(f"正在更新检查点文件，添加结果数据...")
        update_checkpoints_with_results(args.checkpoint_dir)
    else:
        print(f"从 {args.checkpoint_dir} 提取实验结果数据...")
        extract_results_from_checkpoints(args.checkpoint_dir, args.output_file)

    print("处理完成！")

if __name__ == "__main__":
    main()
