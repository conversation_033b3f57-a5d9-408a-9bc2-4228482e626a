import torch
import torch.nn as nn
import torch.nn.functional as F
import random

class FeatureDistillation(nn.Module):
    def __init__(self, margin, alpha, device=None):
        super(FeatureDistillation, self).__init__()
        self.margin = margin  # 用于计算损失的margin参数
        self.alpha = alpha  # 用于平衡损失中的不同部分
        self.device = device if device is not None else torch.device("cuda" if torch.cuda.is_available() else "cpu")

    def forward(self, sentence_embeddings, prev_sentence_embeddings, input_relation_emb,
                input_relation_head_emb, input_relation_tail_emb, num_neg_sample):
        # 计算e1_h和e2_h
        e1_h, e2_h = self.extract_entity_features(sentence_embeddings)
        prev_e1_h, prev_e2_h = self.extract_entity_features(prev_sentence_embeddings)

        # 初始化损失为0
        loss = torch.tensor(0.).to(self.device)
        zeros = torch.tensor(0.).to(self.device)

        for a, (sent_emb, e1, e2) in enumerate(zip(sentence_embeddings, e1_h, e2_h)):
            matched_sentence_pair = input_relation_emb[a]
            matched_head_pair = input_relation_head_emb[a]
            matched_tail_pair = input_relation_tail_emb[a]

            # 计算正样本的相似度
            pos_s = torch.cosine_similarity(matched_sentence_pair, sent_emb, dim=-1).to(self.device)
            pos_h = torch.cosine_similarity(matched_head_pair, e1, dim=-1).max().to(self.device)
            pos_t = torch.cosine_similarity(matched_tail_pair, e2, dim=-1).max().to(self.device)
            pos = (1 - 2 * self.alpha) * pos_s + self.alpha * pos_h + self.alpha * pos_t

            # 随机采样负样本
            sample_size = min(len(input_relation_emb), num_neg_sample)
            #print("input_relation_emb的长度(1)为：",len(input_relation_emb))
            #print("num_neg_sample的数值(1)为：",num_neg_sample)
            if sample_size > 0:
                rand = random.sample(range(len(input_relation_emb)), sample_size)
                #print("input_relation_emb的长度为：",len(input_relation_emb))
                #print("num_neg_sample的数值为：",num_neg_sample)
                neg_relation_emb = torch.stack([input_relation_emb[i] for i in rand])
                neg_relation_head_emb = torch.stack([input_relation_head_emb[i] for i in rand])
                neg_relation_tail_emb = torch.stack([input_relation_tail_emb[i] for i in rand])
            else:
                # 如果没有足够的负样本，初始化为空张量
                neg_relation_emb = torch.tensor([]).to(self.device)
                neg_relation_head_emb = torch.tensor([]).to(self.device)
                neg_relation_tail_emb = torch.tensor([]).to(self.device)


            max_val = torch.tensor(0.).to(self.device)
            for neg_emb, neg_head, neg_tail in zip(neg_relation_emb, neg_relation_head_emb, neg_relation_tail_emb):
                tmp_s = torch.cosine_similarity(sent_emb, neg_emb, dim=-1).to(self.device)
                tmp_h = torch.cosine_similarity(e1, neg_head, dim=-1).max().to(self.device)
                tmp_t = torch.cosine_similarity(e2, neg_tail, dim=-1).max().to(self.device)
                tmp = (1 - 2 * self.alpha) * tmp_s + self.alpha * tmp_h + self.alpha * tmp_t
                if tmp > max_val:
                    max_val = tmp

            neg = max_val.to(self.device)
            loss += torch.max(zeros, neg - pos + self.margin)

        return loss

    def extract_entity_features(self, sequence_output):
        # 检查张量的维度，如果是二维张量，调整为三维张量
        if sequence_output.dim() == 2:
            sequence_output = sequence_output.unsqueeze(1)
        # 从序列输出中提取实体特征（e1_h和e2_h）
        # 假设sequence_output是从模型的BERT层输出的序列
        e1_h = sequence_output[:, 0, :]  # 假设第一个位置代表第一个实体
        e2_h = sequence_output[:, -1, :]  # 假设最后一个位置代表第二个实体
        return e1_h, e2_h
