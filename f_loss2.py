import torch
import torch.nn as nn
import torch.nn.functional as F

class FeatureDistillationModule(nn.Module):
    def __init__(self, hidden_size):
        super(FeatureDistillationModule, self).__init__()
        # 查询向量，用于聚合与关系无关的特征
        self.query_code = nn.Parameter(torch.randn(hidden_size))
        # 用于关系分类的全连接层
        self.relation_classifier = nn.Linear(hidden_size, hidden_size)
        # 梯度反转层的系数
        self.lambda_ = 0.5

    def forward(self, context_embedding):
        # 聚合与关系无关的特征
        irrelevant_features = torch.matmul(context_embedding, self.query_code)
        irrelevant_features = torch.softmax(irrelevant_features, dim=-1)
        aggregated_irrelevant = torch.sum(irrelevant_features.unsqueeze(-1) * context_embedding, dim=0)

        # 通过梯度反转层和关系分类器
        reversed_grad = F.linear(aggregated_irrelevant, self.relation_classifier.weight, self.relation_classifier.bias)
        reversed_grad = F.gradient_reverse_lambda(reversed_grad, self.lambda_)

        # 优化查询向量q，使得基于关系无关特征的分类器性能下降
        classifier_loss = F.cross_entropy(self.relation_classifier(reversed_grad), torch.arange(self.relation_classifier.weight.size(0)))
        
        # 投影到关系无关特征的正交空间
        projection = context_embedding - torch.matmul(context_embedding, aggregated_irrelevant) * aggregated_irrelevant
        refined_embedding = context_embedding - projection

        return refined_embedding, classifier_loss

# 梯度反转函数
def gradient_reverse_lambda(x, lambda_):
    return x * -lambda_

# 以下是如何在模型中使用 FeatureDistillationModule 的示例，您可以根据自己的模型架构进行调整：
# hidden_size = 768  # 根据实际情况调整隐藏层大小
# feature_distillation = FeatureDistillationModule(hidden_size)

# 在模型的训练循环中，您可以这样使用 FeatureDistillationModule：
# context_embedding = ...  # 从模型编码器获得的上下文嵌入
# refined_embedding, classifier_loss = feature_distillation(context_embedding)