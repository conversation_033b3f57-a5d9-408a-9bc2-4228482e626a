import argparse
import random
import os
import time
from sampler import data_sampler
from config import Config
import torch
from model.bert_encoder import Bert_Encoder
from model.dropout_layer import Dropout_Layer
from model.classifier import Softmax_Layer, Proto_Softmax_Layer
from data_loader import get_data_loader
import torch.nn.functional as F
import torch.nn as nn
import torch.optim as optim
import numpy as np
from sklearn.cluster import KMeans
import collections
from copy import deepcopy
from f_loss1 import FeatureDistillation
from p_loss import DistillationRelationExtraction
from checkpoint import save_checkpoint, load_checkpoint, get_latest_checkpoint, create_checkpoint_state
from ssr import SSR  # 导入SSR模块
# os.environ["CUDA_LAUNCH_BLOCKING"] = "1"


def train_simple_model(config, encoder, dropout_layer, classifier, training_data, epochs, map_relid2tempid):
    data_loader = get_data_loader(config, training_data, shuffle=True)

    encoder.train()
    dropout_layer.train()
    classifier.train()

    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam([
        {'params': encoder.parameters(), 'lr': 0.00001},
        {'params': dropout_layer.parameters(), 'lr': 0.00001},
        {'params': classifier.parameters(), 'lr': 0.001}
    ])
    for epoch_i in range(epochs):
        losses = []
        for step, batch_data in enumerate(data_loader):
            optimizer.zero_grad()
            labels, _, tokens = batch_data
            labels = labels.to(config.device)
            labels = [map_relid2tempid[x.item()] for x in labels]
            labels = torch.tensor(labels).to(config.device)

            tokens = torch.stack([x.to(config.device) for x in tokens],dim=0)
            reps = encoder(tokens)
            reps, _ = dropout_layer(reps)
            logits = classifier(reps)
            loss = criterion(logits, labels)

            losses.append(loss.item())
            loss.backward()
            optimizer.step()
        print(f"loss is {np.array(losses).mean()}")


def compute_jsd_loss(m_input):
    # m_input: the result of m times dropout after the classifier.
    # size: m*B*C
    m = m_input.shape[0]
    mean = torch.mean(m_input, dim=0)
    jsd = 0
    for i in range(m):
        loss = F.kl_div(F.log_softmax(mean, dim=-1), F.softmax(m_input[i], dim=-1), reduction='none')
        loss = loss.sum()
        jsd += loss / m
    return jsd


def contrastive_loss(hidden, labels):

    logsoftmax = nn.LogSoftmax(dim=-1)

    return -(logsoftmax(hidden) * labels).sum() / labels.sum()


def construct_hard_triplets(output, labels, relation_data):
    positive = []
    negative = []
    pdist = nn.PairwiseDistance(p=2)
    for rep, label in zip(output, labels):
        positive_relation_data = relation_data[label.item()]
        negative_relation_data = []
        for key in relation_data.keys():
            if key != label.item():
                negative_relation_data.extend(relation_data[key])
        positive_distance = torch.stack([pdist(rep.cpu(), p) for p in positive_relation_data])
        negative_distance = torch.stack([pdist(rep.cpu(), n) for n in negative_relation_data])
        positive_index = torch.argmax(positive_distance)
        negative_index = torch.argmin(negative_distance)
        positive.append(positive_relation_data[positive_index.item()])
        negative.append(negative_relation_data[negative_index.item()])


    return positive, negative


def train_first(config, encoder, dropout_layer, classifier, training_data, epochs, map_relid2tempid, new_relation_data):
    data_loader = get_data_loader(config, training_data, shuffle=True)

    encoder.train()
    dropout_layer.train()
    classifier.train()

    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam([
        {'params': encoder.parameters(), 'lr': 0.00001},
        {'params': dropout_layer.parameters(), 'lr': 0.00001},
        {'params': classifier.parameters(), 'lr': 0.001}
    ])
    triplet_loss = nn.TripletMarginLoss(margin=1.0, p=2)
    for epoch_i in range(epochs):
        losses = []
        for step, (labels, _, tokens) in enumerate(data_loader):

            optimizer.zero_grad()

            logits_all = []
            tokens = torch.stack([x.to(config.device) for x in tokens], dim=0)
            labels = labels.to(config.device)
            origin_labels = labels[:]
            labels = [map_relid2tempid[x.item()] for x in labels]
            labels = torch.tensor(labels).to(config.device)
            reps = encoder(tokens)
            outputs,_ = dropout_layer(reps)
            positives,negatives = construct_hard_triplets(outputs, origin_labels, new_relation_data)

            for _ in range(config.f_pass):
                output, output_embedding = dropout_layer(reps)
                logits = classifier(output)
                logits_all.append(logits)

            positives = torch.cat(positives, 0).to(config.device)
            negatives = torch.cat(negatives, 0).to(config.device)
            anchors = outputs
            logits_all = torch.stack(logits_all)
            m_labels = labels.expand((config.f_pass, labels.shape[0]))  # m,B
            loss1 = criterion(logits_all.reshape(-1, logits_all.shape[-1]), m_labels.reshape(-1))
            loss2 = compute_jsd_loss(logits_all)
            tri_loss = triplet_loss(anchors, positives, negatives)
            loss = loss1 + loss2 + tri_loss

            loss.backward()
            losses.append(loss.item())
            optimizer.step()
        print(f"loss is {np.array(losses).mean()}")

def train_mem_model(config, encoder, dropout_layer, classifier, training_data, epochs, map_relid2tempid, new_relation_data,
                prev_encoder, prev_dropout_layer, prev_classifier, prev_relation_index):
    data_loader = get_data_loader(config, training_data, shuffle=True)

    encoder.train()
    dropout_layer.train()
    classifier.train()

    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam([
        {'params': encoder.parameters(), 'lr': 0.00001},
        {'params': dropout_layer.parameters(), 'lr': 0.00001},
        {'params': classifier.parameters(), 'lr': 0.001}
    ])
    triplet_loss = nn.TripletMarginLoss(margin=1.0, p=2)
    distill_criterion = nn.CosineEmbeddingLoss()

    # +++实例化FeatureDistillation类
    feature_distillation = FeatureDistillation(margin=0.06, alpha=0.33, device=config.device)

    # +++实例化DistillationRelationExtraction类
    distill_model = DistillationRelationExtraction(
        entity_dim=config.encoder_output_size,
        context_dim=config.encoder_output_size,
        num_labels=len(map_relid2tempid),
        hidden_dim=config.encoder_output_size,
        lambda_=config.temp
    ).to(config.device)

    T = config.kl_temp
    for epoch_i in range(epochs):
        losses = []
        for step, (labels, _, tokens) in enumerate(data_loader):

            optimizer.zero_grad()

            logits_all = []

            # 处理不同长度的样本
            try:
                # 尝试使用批处理
                tokens = torch.stack([x.to(config.device) for x in tokens], dim=0)
                batch_processing = True
            except RuntimeError as e:
                if "stack expects each tensor to be equal size" in str(e):
                    # 如果样本长度不同，使用单样本处理
                    print("【批处理警告】检测到不同长度的样本，切换到单样本处理模式")
                    batch_processing = False
                else:
                    # 其他错误，直接抛出
                    raise e

            labels = labels.to(config.device)
            origin_labels = labels[:]
            labels = [map_relid2tempid[x.item()] for x in labels]
            labels = torch.tensor(labels).to(config.device)

            if batch_processing:
                # 批处理模式
                reps = encoder(tokens)
                normalized_reps_emb = F.normalize(reps.view(-1, reps.size()[1]), p=2, dim=1)
                outputs, _ = dropout_layer(reps)
                if prev_dropout_layer is not None:
                    prev_outputs, _ = prev_dropout_layer(reps)
                    positives, negatives = construct_hard_triplets(prev_outputs, origin_labels, new_relation_data)
                else:
                    positives, negatives = construct_hard_triplets(outputs, origin_labels, new_relation_data)
            else:
                # 单样本处理模式
                batch_reps = []
                batch_outputs = []
                batch_prev_outputs = []

                for i, token in enumerate(tokens):
                    # 处理单个样本
                    token = token.unsqueeze(0).to(config.device)
                    rep = encoder(token)
                    batch_reps.append(rep)
                    output, _ = dropout_layer(rep)
                    batch_outputs.append(output)

                    if prev_dropout_layer is not None:
                        prev_output, _ = prev_dropout_layer(rep)
                        batch_prev_outputs.append(prev_output)

                # 合并结果
                reps = torch.cat(batch_reps, dim=0)
                normalized_reps_emb = F.normalize(reps.view(-1, reps.size()[1]), p=2, dim=1)
                outputs = torch.cat(batch_outputs, dim=0)

                if prev_dropout_layer is not None:
                    prev_outputs = torch.cat(batch_prev_outputs, dim=0)
                    positives, negatives = construct_hard_triplets(prev_outputs, origin_labels, new_relation_data)
                else:
                    positives, negatives = construct_hard_triplets(outputs, origin_labels, new_relation_data)

            for _ in range(config.f_pass):
                output, output_embedding = dropout_layer(reps)
                logits = classifier(output)
                logits_all.append(logits)

            positives = torch.cat(positives, 0).to(config.device)
            negatives = torch.cat(negatives, 0).to(config.device)
            anchors = outputs
            logits_all = torch.stack(logits_all)
            m_labels = labels.expand((config.f_pass, labels.shape[0]))  # m,B
            loss1 = criterion(logits_all.reshape(-1, logits_all.shape[-1]), m_labels.reshape(-1))
            loss2 = compute_jsd_loss(logits_all)
            tri_loss = triplet_loss(anchors, positives, negatives)
            loss = loss1 + loss2 + tri_loss

            if prev_encoder is not None:
                # 确保tokens是张量而不是列表
                if batch_processing:
                    # 批处理模式，tokens已经是张量
                    prev_reps = prev_encoder(tokens).detach()
                else:
                    # 单样本处理模式，需要处理每个样本
                    batch_prev_reps = []
                    for token in tokens:
                        token = token.unsqueeze(0).to(config.device)
                        prev_rep = prev_encoder(token).detach()
                        batch_prev_reps.append(prev_rep)
                    prev_reps = torch.cat(batch_prev_reps, dim=0)

                normalized_prev_reps_emb = F.normalize(prev_reps.view(-1, prev_reps.size()[1]), p=2, dim=1)

                # 使用FeatureDistillation计算特征蒸馏损失
                prev_e1_h, prev_e2_h = feature_distillation.extract_entity_features(prev_reps)
                feature_distill_loss = feature_distillation(
                    sentence_embeddings=normalized_reps_emb,
                    prev_sentence_embeddings=normalized_prev_reps_emb,
                    input_relation_emb=prev_reps,
                    input_relation_head_emb=prev_e1_h,
                    input_relation_tail_emb=prev_e2_h,
                    num_neg_sample=7
                )

                #print("跑完的代码11")
                # feature_distill_loss = distill_criterion(normalized_reps_emb, normalized_prev_reps_emb,
                #                                          torch.ones(tokens.size(0)).to(
                #                                              config.device))
                #print("这里这里是feature_distill_loss的输出值：",feature_distill_loss)
                #feature_distill_loss = rematch
                loss += feature_distill_loss
            #print("跑完的代码22")
            if prev_dropout_layer is not None and prev_classifier is not None:
                prediction_distill_loss = None
                dropout_output_all = []
                prev_dropout_output_all = []
                for i in range(config.f_pass):
                    output, _ = dropout_layer(reps)

                    # 确保reps是张量而不是列表
                    if batch_processing:
                        # 批处理模式，reps已经是张量
                        prev_output, _ = prev_dropout_layer(reps)
                    else:
                        # 单样本处理模式，已经在前面处理过reps，这里可以直接使用
                        prev_output, _ = prev_dropout_layer(reps)

                    dropout_output_all.append(output)
                    prev_dropout_output_all.append(prev_output)  # 修正：使用prev_output而不是output
                    pre_logits = prev_classifier(prev_output).detach()  # 修正：使用prev_output而不是output

                    pre_logits = F.softmax(pre_logits.index_select(1, prev_relation_index) / T, dim=1)

                    log_logits = F.log_softmax(logits_all[i].index_select(1, prev_relation_index) / T, dim=1)
                    if i == 0:
                        prediction_distill_loss = -torch.mean(torch.sum(pre_logits * log_logits, dim=1))
                    else:
                        prediction_distill_loss += -torch.mean(torch.sum(pre_logits * log_logits, dim=1))

                prediction_distill_loss /= config.f_pass
                loss += prediction_distill_loss
                dropout_output_all = torch.stack(dropout_output_all)
                prev_dropout_output_all = torch.stack(prev_dropout_output_all)
                mean_dropout_output_all = torch.mean(dropout_output_all, dim=0)
                mean_prev_dropout_output_all = torch.mean(prev_dropout_output_all,dim=0)
                normalized_output = F.normalize(mean_dropout_output_all.view(-1, mean_dropout_output_all.size()[1]), p=2, dim=1)
                normalized_prev_output = F.normalize(mean_prev_dropout_output_all.view(-1, mean_prev_dropout_output_all.size()[1]), p=2, dim=1)
                # hidden_distill_loss = distill_criterion(normalized_output, normalized_prev_output,
                #                                          torch.ones(tokens.size(0)).to(
                #                                              config.device))
                # +++使用DistillationRelationExtraction计算hidden_distill_loss
                hidden_distill_loss = distill_model(
                    tokens=tokens,
                    sentence_embeddings=normalized_output,
                    common_emb=normalized_prev_output,
                    labels=labels
                )
                loss += hidden_distill_loss

            loss.backward()
            losses.append(loss.item())
            optimizer.step()
        print(f"loss is {np.array(losses).mean()}")


# def train_mem_model(config, encoder, dropout_layer, classifier, training_data, epochs, map_relid2tempid, new_relation_data,
#                 prev_encoder, prev_dropout_layer, prev_classifier, prev_relation_index):
#     data_loader = get_data_loader(config, training_data, shuffle=True)

#     encoder.train()
#     dropout_layer.train()
#     classifier.train()

#     criterion = nn.CrossEntropyLoss()
#     optimizer = optim.Adam([
#         {'params': encoder.parameters(), 'lr': 0.00001},
#         {'params': dropout_layer.parameters(), 'lr': 0.00001},
#         {'params': classifier.parameters(), 'lr': 0.001}
#     ])
#     triplet_loss = nn.TripletMarginLoss(margin=1.0, p=2)
#     distill_criterion = nn.CosineEmbeddingLoss()
#     T = config.kl_temp
#     for epoch_i in range(epochs):
#         losses = []
#         for step, (labels, _, tokens) in enumerate(data_loader):

#             optimizer.zero_grad()

#             logits_all = []
#             tokens = torch.stack([x.to(config.device) for x in tokens], dim=0)
#             labels = labels.to(config.device)
#             origin_labels = labels[:]
#             labels = [map_relid2tempid[x.item()] for x in labels]
#             labels = torch.tensor(labels).to(config.device)
#             reps = encoder(tokens)
#             normalized_reps_emb = F.normalize(reps.view(-1, reps.size()[1]), p=2, dim=1)
#             outputs,_ = dropout_layer(reps)
#             if prev_dropout_layer is not None:
#                 prev_outputs, _ = prev_dropout_layer(reps)
#                 positives,negatives = construct_hard_triplets(prev_outputs, origin_labels, new_relation_data)
#             else:
#                 positives, negatives = construct_hard_triplets(outputs, origin_labels, new_relation_data)

#             for _ in range(config.f_pass):
#                 output, output_embedding = dropout_layer(reps)
#                 logits = classifier(output)
#                 logits_all.append(logits)

#             positives = torch.cat(positives, 0).to(config.device)
#             negatives = torch.cat(negatives, 0).to(config.device)
#             anchors = outputs
#             logits_all = torch.stack(logits_all)
#             m_labels = labels.expand((config.f_pass, labels.shape[0]))  # m,B
#             loss1 = criterion(logits_all.reshape(-1, logits_all.shape[-1]), m_labels.reshape(-1))
#             loss2 = compute_jsd_loss(logits_all)
#             tri_loss = triplet_loss(anchors, positives, negatives)
#             loss = loss1 + loss2 + tri_loss

#             if prev_encoder is not None:
#                 prev_reps = prev_encoder(tokens).detach()
#                 normalized_prev_reps_emb = F.normalize(prev_reps.view(-1, prev_reps.size()[1]), p=2, dim=1)

#                 feature_distill_loss = distill_criterion(normalized_reps_emb, normalized_prev_reps_emb,
#                                                          torch.ones(tokens.size(0)).to(
#                                                              config.device))
#                 #print("这里这里是feature_distill_loss的输出值：",feature_distill_loss)
#                 #feature_distill_loss = rematch
#                 loss += feature_distill_loss

#             if prev_dropout_layer is not None and prev_classifier is not None:
#                 prediction_distill_loss = None
#                 dropout_output_all = []
#                 prev_dropout_output_all = []
#                 for i in range(config.f_pass):
#                     output, _ = dropout_layer(reps)
#                     prev_output, _ = prev_dropout_layer(reps)
#                     dropout_output_all.append(output)
#                     prev_dropout_output_all.append(output)
#                     pre_logits = prev_classifier(output).detach()

#                     pre_logits = F.softmax(pre_logits.index_select(1, prev_relation_index) / T, dim=1)

#                     log_logits = F.log_softmax(logits_all[i].index_select(1, prev_relation_index) / T, dim=1)
#                     if i == 0:
#                         prediction_distill_loss = -torch.mean(torch.sum(pre_logits * log_logits, dim=1))
#                     else:
#                         prediction_distill_loss += -torch.mean(torch.sum(pre_logits * log_logits, dim=1))

#                 prediction_distill_loss /= config.f_pass
#                 loss += prediction_distill_loss
#                 dropout_output_all = torch.stack(dropout_output_all)
#                 prev_dropout_output_all = torch.stack(prev_dropout_output_all)
#                 mean_dropout_output_all = torch.mean(dropout_output_all, dim=0)
#                 mean_prev_dropout_output_all = torch.mean(prev_dropout_output_all,dim=0)
#                 normalized_output = F.normalize(mean_dropout_output_all.view(-1, mean_dropout_output_all.size()[1]), p=2, dim=1)
#                 normalized_prev_output = F.normalize(mean_prev_dropout_output_all.view(-1, mean_prev_dropout_output_all.size()[1]), p=2, dim=1)
#                 hidden_distill_loss = distill_criterion(normalized_output, normalized_prev_output,
#                                                          torch.ones(tokens.size(0)).to(
#                                                              config.device))
#                 loss += hidden_distill_loss

#             loss.backward()
#             losses.append(loss.item())
#             optimizer.step()
#         print(f"loss is {np.array(losses).mean()}")


def batch2device(batch_tuple, device):
    ans = []
    for var in batch_tuple:
        if isinstance(var, torch.Tensor):
            ans.append(var.to(device))
        elif isinstance(var, list):
            ans.append(batch2device(var))
        elif isinstance(var, tuple):
            ans.append(tuple(batch2device(var)))
        else:
            ans.append(var)
    return ans


def evaluate_strict_model(config, encoder, dropout_layer, classifier, test_data, seen_relations, map_relid2tempid):
    data_loader = get_data_loader(config, test_data, batch_size=1)
    encoder.eval()
    dropout_layer.eval()
    classifier.eval()
    n = len(test_data)

    correct = 0
    for step, batch_data in enumerate(data_loader):
        labels, _, tokens = batch_data
        labels = labels.to(config.device)
        labels = [map_relid2tempid[x.item()] for x in labels]
        labels = torch.tensor(labels).to(config.device)

        # 处理不同长度的样本
        try:
            # 尝试使用批处理
            tokens = torch.stack([x.to(config.device) for x in tokens], dim=0)
            reps = encoder(tokens)
            reps, _ = dropout_layer(reps)
            logits = classifier(reps)
        except RuntimeError as e:
            if "stack expects each tensor to be equal size" in str(e):
                # 如果样本长度不同，使用单样本处理
                print("【评估警告】检测到不同长度的样本，切换到单样本处理模式")
                # 处理单个样本
                token = tokens[0].unsqueeze(0).to(config.device)
                rep = encoder(token)
                rep, _ = dropout_layer(rep)
                logits = classifier(rep)
            else:
                # 其他错误，直接抛出
                raise e

        seen_relation_ids = [rel2id[relation] for relation in seen_relations]
        seen_relation_ids = [map_relid2tempid[relation] for relation in seen_relation_ids]
        seen_sim = logits[:,seen_relation_ids].cpu().data.numpy()
        max_smi = np.max(seen_sim,axis=1)

        label_smi = logits[:,labels].cpu().data.numpy()

        if label_smi >= max_smi:
            correct += 1

    return correct/n


def select_data(config, encoder, dropout_layer, relation_dataset):
    data_loader = get_data_loader(config, relation_dataset, shuffle=False, drop_last=False, batch_size=1)
    features = []
    encoder.eval()
    dropout_layer.eval()
    for step, batch_data in enumerate(data_loader):
        labels, _, tokens = batch_data
        try:
            # 尝试使用批处理
            tokens = torch.stack([x.to(config.device) for x in tokens], dim=0)
            with torch.no_grad():
                feature = dropout_layer(encoder(tokens))[1].cpu()
            features.append(feature)
        except RuntimeError as e:
            if "stack expects each tensor to be equal size" in str(e):
                # 如果样本长度不同，使用单样本处理
                print("【样本选择警告】检测到不同长度的样本，切换到单样本处理模式")
                # 处理单个样本
                token = tokens[0].unsqueeze(0).to(config.device)
                with torch.no_grad():
                    feature = dropout_layer(encoder(token))[1].cpu()
                features.append(feature)
            else:
                # 其他错误，直接抛出
                raise e

    features = np.concatenate(features)
    num_clusters = min(config.num_protos, len(relation_dataset))
    distances = KMeans(n_clusters=num_clusters, random_state=0).fit_transform(features)

    memory = []
    for k in range(num_clusters):
        sel_index = np.argmin(distances[:, k])
        instance = relation_dataset[sel_index]
        memory.append(instance)
    return memory


def get_proto(config, encoder, dropout_layer, relation_dataset):
    data_loader = get_data_loader(config, relation_dataset, shuffle=False, drop_last=False, batch_size=1)
    features = []
    encoder.eval()
    dropout_layer.eval()
    for step, batch_data in enumerate(data_loader):
        labels, _, tokens = batch_data
        try:
            # 尝试使用批处理
            tokens = torch.stack([x.to(config.device) for x in tokens], dim=0)
            with torch.no_grad():
                feature = dropout_layer(encoder(tokens))[1]
            features.append(feature)
        except RuntimeError as e:
            if "stack expects each tensor to be equal size" in str(e):
                # 如果样本长度不同，使用单样本处理
                print("【原型获取警告】检测到不同长度的样本，切换到单样本处理模式")
                # 处理单个样本
                token = tokens[0].unsqueeze(0).to(config.device)
                with torch.no_grad():
                    feature = dropout_layer(encoder(token))[1]
                features.append(feature)
            else:
                # 其他错误，直接抛出
                raise e
    features = torch.cat(features, dim=0)
    proto = torch.mean(features, dim=0, keepdim=True).cpu()
    standard = torch.sqrt(torch.var(features, dim=0)).cpu()
    return proto, standard


def generate_relation_data(protos, relation_standard):
    relation_data = {}
    relation_sample_nums = 10
    for id in protos.keys():
        relation_data[id] = []
        difference = np.random.normal(loc=0, scale=1, size=relation_sample_nums)
        for diff in difference:
            relation_data[id].append(protos[id] + diff * relation_standard[id])
    return relation_data


def generate_current_relation_data(config, encoder, dropout_layer, relation_dataset):
    data_loader = get_data_loader(config, relation_dataset, shuffle=False, drop_last=False, batch_size=1)
    relation_data = []
    encoder.eval()
    dropout_layer.eval()
    for step, batch_data in enumerate(data_loader):
        labels, _, tokens = batch_data
        try:
            # 尝试使用批处理
            tokens = torch.stack([x.to(config.device) for x in tokens], dim=0)
            with torch.no_grad():
                feature = dropout_layer(encoder(tokens))[1].cpu()
            relation_data.append(feature)
        except RuntimeError as e:
            if "stack expects each tensor to be equal size" in str(e):
                # 如果样本长度不同，使用单样本处理
                print("【关系数据生成警告】检测到不同长度的样本，切换到单样本处理模式")
                # 处理单个样本
                token = tokens[0].unsqueeze(0).to(config.device)
                with torch.no_grad():
                    feature = dropout_layer(encoder(token))[1].cpu()
                relation_data.append(feature)
            else:
                # 其他错误，直接抛出
                raise e
    return relation_data

from transformers import  BertTokenizer
def data_augmentation(config, encoder, train_data, prev_train_data):
    expanded_train_data = train_data[:]
    expanded_prev_train_data = prev_train_data[:]
    encoder.eval()
    all_data = train_data + prev_train_data
    tokenizer = BertTokenizer.from_pretrained(config.bert_path, additional_special_tokens=["[E11]", "[E12]", "[E21]", "[E22]"])
    entity_index = []
    entity_mention = []
    for sample in all_data:
        e11 = sample['tokens'].index(30522)
        e12 = sample['tokens'].index(30523)
        e21 = sample['tokens'].index(30524)
        e22 = sample['tokens'].index(30525)
        entity_index.append([e11,e12])
        entity_mention.append(sample['tokens'][e11+1:e12])
        entity_index.append([e21,e22])
        entity_mention.append(sample['tokens'][e21+1:e22])

    data_loader = get_data_loader(config, all_data, shuffle=False, drop_last=False, batch_size=1)
    features = []
    encoder.eval()
    for step, batch_data in enumerate(data_loader):
        labels, _, tokens = batch_data
        try:
            # 尝试使用批处理
            tokens = torch.stack([x.to(config.device) for x in tokens], dim=0)
            with torch.no_grad():
                feature = encoder(tokens)
            feature1, feature2 = torch.split(feature, [config.encoder_output_size,config.encoder_output_size], dim=1)
            features.append(feature1)
            features.append(feature2)
        except RuntimeError as e:
            if "stack expects each tensor to be equal size" in str(e):
                # 如果样本长度不同，使用单样本处理
                print("【数据增强警告】检测到不同长度的样本，切换到单样本处理模式")
                # 处理单个样本
                token = tokens[0].unsqueeze(0).to(config.device)
                with torch.no_grad():
                    feature = encoder(token)
                feature1, feature2 = torch.split(feature, [config.encoder_output_size,config.encoder_output_size], dim=1)
                features.append(feature1)
                features.append(feature2)
            else:
                # 其他错误，直接抛出
                raise e
    features = torch.cat(features, dim=0)
    # similarity_matrix = F.cosine_similarity(features.unsqueeze(1), features.unsqueeze(0), dim=-1)
    similarity_matrix = []
    for i in range(len(features)):
        similarity_matrix.append([0]*len(features))

    for i in range(len(features)):
        for j in range(i,len(features)):
            similarity = F.cosine_similarity(features[i],features[j],dim=0)
            similarity_matrix[i][j] = similarity
            similarity_matrix[j][i] = similarity

    similarity_matrix = torch.tensor(similarity_matrix).to(config.device)
    zero = torch.zeros_like(similarity_matrix).to(config.device)
    diag = torch.diag_embed(torch.diag(similarity_matrix))
    similarity_matrix -= diag
    similarity_matrix = torch.where(similarity_matrix<0.95, zero, similarity_matrix)
    nonzero_index = torch.nonzero(similarity_matrix)
    expanded_train_count = 0

    for origin, replace in nonzero_index:
        sample_index = int(origin/2)
        sample = all_data[sample_index]
        if entity_mention[origin] == entity_mention[replace]:
            continue
        new_tokens = sample['tokens'][:entity_index[origin][0]+1] + entity_mention[replace] + sample['tokens'][entity_index[origin][1]:]
        if len(new_tokens) < config.max_length:
            new_tokens = new_tokens + [0]*(config.max_length-len(new_tokens))
        else:
            new_tokens = new_tokens[:config.max_length]

        new_sample = {
            'relation': sample['relation'],
            'neg_labels': sample['neg_labels'],
            'tokens': new_tokens
        }
        if sample_index < len(train_data) and expanded_train_count < 5 * len(train_data):
            expanded_train_data.append(new_sample)
            expanded_train_count += 1
        else:
            expanded_prev_train_data.append(new_sample)
    return expanded_train_data, expanded_prev_train_data


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument("--task", default="tacred", type=str)
    parser.add_argument("--shot", default=10, type=str)
    parser.add_argument('--config', default='config.ini')
    parser.add_argument('--checkpoint_dir', default='checkpoints', help='检查点保存目录')
    parser.add_argument('--resume', action='store_true', help='从最新检查点恢复训练')
    parser.add_argument('--no_resume', action='store_true', help='不从检查点恢复训练，重新开始')
    parser.add_argument('--checkpoint', default=None, help='指定检查点文件路径')
    parser.add_argument('--save_interval', type=int, default=1, help='每隔多少步保存一次检查点')
    parser.add_argument('--keep_latest_only', action='store_true', help='只保留最新的检查点')
    args = parser.parse_args()
    config = Config(args.config)

    # 强制使用CPU设备
    config.device = torch.device("cpu")
    print(f"强制使用CPU进行训练，这可能会导致训练速度较慢")
    config.n_gpu = 0  # 使用CPU时，GPU数量为0
    config.batch_size_per_step = int(config.batch_size / config.gradient_accumulation_steps)

    # 减小批量大小以适应CPU训练
    config.batch_size = min(config.batch_size, 16)  # 减小批量大小
    config.batch_size_per_step = int(config.batch_size / config.gradient_accumulation_steps)

    config.task = args.task
    config.shot = args.shot
    # 减少训练轮数以加快测试速度
    config.step1_epochs = 1  # 原来是5
    config.step2_epochs = 2  # 原来是15
    config.step3_epochs = 2  # 原来是20
    config.temperature = 0.08

    # 设置SSR参数
    config.num_synthetic_per_relation = 10  # 每个关系生成的伪样本数量
    config.diversity_threshold = 0.5  # 降低伪样本多样性阈值
    config.quality_threshold = 0.3  # 大幅降低伪样本质量阈值
    config.enable = True  # 启用SSR
    config.debug = True   # 启用调试模式，输出更多信息

    # 创建检查点目录
    if not os.path.exists(args.checkpoint_dir):
        os.makedirs(args.checkpoint_dir)

    # 检查是否需要从检查点恢复
    checkpoint_to_load = None
    if args.no_resume:
        print("不从检查点恢复训练，重新开始...")
    elif args.checkpoint:
        checkpoint_to_load = args.checkpoint
    elif args.resume:
        checkpoint_to_load = get_latest_checkpoint(args.checkpoint_dir)

    if config.task == "FewRel":
        config.relation_file = "data/fewrel/relation_name.txt"
        config.rel_index = "data/fewrel/rel_index.npy"
        config.rel_feature = "data/fewrel/rel_feature.npy"
        config.rel_des_file = "data/fewrel/relation_description.txt"
        config.num_of_relation = 80
        if config.shot == '5':
            config.rel_cluster_label = "data/fewrel/CFRLdata_10_100_10_5/rel_cluster_label_0.npy"
            config.training_file = "data/fewrel/CFRLdata_10_100_10_5/train_0.txt"
            config.valid_file = "data/fewrel/CFRLdata_10_100_10_5/valid_0.txt"
            config.test_file = "data/fewrel/CFRLdata_10_100_10_5/test_0.txt"
        elif config.shot == '10':
            config.rel_cluster_label = "data/fewrel/CFRLdata_10_100_10_10/rel_cluster_label_0.npy"
            config.training_file = "data/fewrel/CFRLdata_10_100_10_10/train_0.txt"
            config.valid_file = "data/fewrel/CFRLdata_10_100_10_10/valid_0.txt"
            config.test_file = "data/fewrel/CFRLdata_10_100_10_10/test_0.txt"
        else:
            config.rel_cluster_label = "data/fewrel/CFRLdata_10_100_10_2/rel_cluster_label_0.npy"
            config.training_file = "data/fewrel/CFRLdata_10_100_10_2/train_0.txt"
            config.valid_file = "data/fewrel/CFRLdata_10_100_10_2/valid_0.txt"
            config.test_file = "data/fewrel/CFRLdata_10_100_10_2/test_0.txt"
    else:
        config.relation_file = "data/tacred/relation_name.txt"
        config.rel_index = "data/tacred/rel_index.npy"
        config.rel_feature = "data/tacred/rel_feature.npy"
        config.num_of_relation = 41
        if config.shot == '5':
            config.rel_cluster_label = "data/tacred/CFRLdata_10_100_10_5/rel_cluster_label_0.npy"
            config.training_file = "data/tacred/CFRLdata_10_100_10_5/train_0.txt"
            config.valid_file = "data/tacred/CFRLdata_10_100_10_5/valid_0.txt"
            config.test_file = "data/tacred/CFRLdata_10_100_10_5/test_0.txt"
        else:
            config.rel_cluster_label = "data/tacred/CFRLdata_10_100_10_10/rel_cluster_label_0.npy"
            config.training_file = "data/tacred/CFRLdata_10_100_10_10/train_0.txt"
            config.valid_file = "data/tacred/CFRLdata_10_100_10_10/valid_0.txt"
            config.test_file = "data/tacred/CFRLdata_10_100_10_10/test_0.txt"

    result_cur_test = []
    result_whole_test = []
    bwt_whole = []
    fwt_whole = []
    X = []
    Y = []
    relation_divides = []
    for i in range(10):
        relation_divides.append([])

    # 从检查点恢复训练状态
    start_round = 0
    start_step = 0
    if checkpoint_to_load:
        print(f"正在从检查点 {checkpoint_to_load} 恢复训练...")
        checkpoint_data = torch.load(checkpoint_to_load, map_location=lambda storage, loc: storage)
        start_round = checkpoint_data.get('round', 0)
        start_step = checkpoint_data.get('step', 0)

        # 恢复全局训练状态
        if 'result_cur_test' in checkpoint_data:
            result_cur_test = checkpoint_data['result_cur_test']
        if 'result_whole_test' in checkpoint_data:
            result_whole_test = checkpoint_data['result_whole_test']
        if 'bwt_whole' in checkpoint_data:
            bwt_whole = checkpoint_data['bwt_whole']
        if 'fwt_whole' in checkpoint_data:
            fwt_whole = checkpoint_data['fwt_whole']
        if 'relation_divides' in checkpoint_data:
            relation_divides = checkpoint_data['relation_divides']

        print(f"恢复训练：从第 {start_round+1} 轮第 {start_step+1} 步开始")

    for rou in range(start_round, config.total_round):
        test_cur = []
        test_total = []
        random.seed(config.seed+rou*100)
        sampler = data_sampler(config=config, seed=config.seed+rou*100)
        id2rel = sampler.id2rel
        rel2id = sampler.rel2id
        id2sentence = sampler.get_id2sent()
        encoder = Bert_Encoder(config=config).to(config.device)
        dropout_layer = Dropout_Layer(config=config).to(config.device)
        num_class = len(sampler.id2rel)

        memorized_samples = {}
        memory = collections.defaultdict(list)
        history_relations = []
        history_data = []
        prev_relations = []
        classifier = None
        prev_classifier = None
        prev_encoder = None
        prev_dropout_layer = None
        relation_standard = {}
        forward_accs = []

        # 如果是恢复训练的第一轮，且有检查点数据，则恢复模型状态
        if rou == start_round and checkpoint_to_load:
            # 恢复模型状态
            if 'encoder_state_dict' in checkpoint_data:
                encoder.load_state_dict(checkpoint_data['encoder_state_dict'])
            if 'dropout_layer_state_dict' in checkpoint_data:
                dropout_layer.load_state_dict(checkpoint_data['dropout_layer_state_dict'])
            if 'classifier_state_dict' in checkpoint_data and checkpoint_data['classifier_state_dict']:
                classifier = Softmax_Layer(input_size=encoder.output_size, num_class=len(checkpoint_data.get('history_relations', []))).to(config.device)
                classifier.load_state_dict(checkpoint_data['classifier_state_dict'])

            # 恢复训练状态
            if 'memorized_samples' in checkpoint_data:
                memorized_samples = checkpoint_data['memorized_samples']
            if 'history_relations' in checkpoint_data:
                history_relations = checkpoint_data['history_relations']
            if 'history_data' in checkpoint_data:
                history_data = checkpoint_data['history_data']
            if 'relation_standard' in checkpoint_data:
                relation_standard = checkpoint_data['relation_standard']
            if 'test_cur' in checkpoint_data:
                test_cur = checkpoint_data['test_cur']
            if 'test_total' in checkpoint_data:
                test_total = checkpoint_data['test_total']
        # 跳过已经完成的步骤
        step_iterator = enumerate(sampler)
        if rou == start_round and start_step > 0:
            # 跳过已经完成的步骤
            for _ in range(start_step):
                try:
                    next(step_iterator)
                except StopIteration:
                    break

        for steps, (training_data, valid_data, test_data, current_relations, historic_test_data, seen_relations) in step_iterator:
            print(current_relations)

            prev_relations = history_relations[:]
            train_data_for_initial = []
            count = 0
            for relation in current_relations:
                history_relations.append(relation)
                train_data_for_initial += training_data[relation]
                relation_divides[count].append(float(rel2id[relation]))
                count += 1


            temp_rel2id = [rel2id[x] for x in seen_relations]
            map_relid2tempid = {k: v for v, k in enumerate(temp_rel2id)}
            prev_relation_index = []
            prev_samples = []
            for relation in prev_relations:
                prev_relation_index.append(map_relid2tempid[rel2id[relation]])
                prev_samples += memorized_samples[relation]
            prev_relation_index = torch.tensor(prev_relation_index).to(config.device)

            classifier = Softmax_Layer(input_size=encoder.output_size, num_class=len(history_relations)).to(
                config.device)

            temp_protos = {}
            for relation in current_relations:
                proto, _ = get_proto(config, encoder, dropout_layer, training_data[relation])
                temp_protos[rel2id[relation]] = proto

            for relation in prev_relations:
                proto, _ = get_proto(config, encoder, dropout_layer, memorized_samples[relation])
                temp_protos[rel2id[relation]] = proto

            test_data_1 = []
            for relation in current_relations:
                test_data_1 += test_data[relation]

            if steps != 0 and prev_encoder is not None and prev_dropout_layer is not None:
                forward_acc = evaluate_strict_model(config, prev_encoder, prev_dropout_layer, classifier, test_data_1, seen_relations, map_relid2tempid)
                forward_accs.append(forward_acc)

            print("\n【开始简单模型训练】")
            print(f"训练数据数量: {len(train_data_for_initial)} 个样本")
            print(f"训练轮数: {config.step1_epochs} 轮")
            train_simple_model(config, encoder, dropout_layer, classifier, train_data_for_initial, config.step1_epochs, map_relid2tempid)
            print(f"【简单模型训练完成】")


            temp_protos = {}

            for relation in current_relations:
                proto, standard = get_proto(config,encoder,dropout_layer,training_data[relation])
                temp_protos[rel2id[relation]] = proto
                relation_standard[rel2id[relation]] = standard


            for relation in prev_relations:
                proto, _ = get_proto(config,encoder,dropout_layer,memorized_samples[relation])
                temp_protos[rel2id[relation]] = proto

            new_relation_data = generate_relation_data(temp_protos, relation_standard)

            for relation in current_relations:
                new_relation_data[rel2id[relation]].extend(generate_current_relation_data(config, encoder,dropout_layer,training_data[relation]))

            # 使用SSR生成伪数据
            try:
                # 检查配置文件中是否启用了SSR
                ssr_enabled = getattr(config, 'enable', True)
            except AttributeError:
                ssr_enabled = True  # 默认启用

            print("\n" + "="*80)
            print(f"【当前训练阶段】第 {rou+1} 轮，第 {steps+1} 步")
            print(f"【当前任务关系】{current_relations}")
            print(f"【历史关系数量】{len(prev_relations)} 个")
            print(f"【历史关系列表】{prev_relations}")
            print(f"【历史样本数量】{len(prev_samples)} 个")
            print("="*80 + "\n")

            if ssr_enabled and prev_relations and prev_encoder is not None and prev_dropout_layer is not None:
                print("使用SSR生成伪数据进行复习...")
                print(f"SSR参数: 质量阈值={config.quality_threshold}, 多样性阈值={config.diversity_threshold}, 每关系样本数={config.num_synthetic_per_relation}")

                print("\n【历史关系样本统计】")
                print("-"*50)
                for relation in prev_relations:
                    print(f"关系 '{relation}' 的样本数量: {len(memorized_samples[relation])}")
                print("-"*50)

                # 初始化SSR模块
                print("\n【初始化SSR模块】")
                ssr_module = SSR(config, encoder, dropout_layer, classifier)

                # 生成伪数据
                print("\n【开始生成伪数据】")
                synthetic_data = ssr_module.generate_synthetic_data(prev_relations, memorized_samples, rel2id)
                print(f"【原始伪样本数量】{len(synthetic_data)} 个")

                # 使用前一个模型优化伪数据质量
                print("\n【开始优化伪数据质量】")
                refined_synthetic_data = ssr_module.refine_synthetic_data(
                    synthetic_data, prev_encoder, prev_dropout_layer, prev_classifier
                )

                print(f"\n【SSR最终结果】生成了 {len(refined_synthetic_data)} 个高质量伪样本用于复习之前的关系")
                print(f"【伪样本保留率】{len(refined_synthetic_data)/max(1, len(synthetic_data)):.2%}")
                print(f"【伪样本与历史样本比例】{len(refined_synthetic_data)/max(1, len(prev_samples)):.2%}")

                # 将伪数据添加到训练数据中
                # 注意：伪样本应该用于第二阶段记忆模型训练，而不是第一阶段
                # 这里先保存伪样本，稍后添加到train_data_for_memory
                synthetic_samples_for_memory = refined_synthetic_data.copy()

                # 如果生成了伪样本，打印样本信息
                if refined_synthetic_data:
                    print("\n【伪样本详细信息】")
                    print("-"*50)

                    # 统计每个关系的伪样本数量
                    relation_counts = {}
                    for sample in refined_synthetic_data:
                        rel_id = sample['relation']
                        rel_name = [name for name, id in rel2id.items() if id == rel_id][0] if rel_id in [rel2id[r] for r in prev_relations] else "未知关系"
                        relation_counts[rel_name] = relation_counts.get(rel_name, 0) + 1

                    print("各关系伪样本数量:")
                    for rel_name, count in relation_counts.items():
                        print(f"  - {rel_name}: {count} 个")

                    # 打印第一个样本的详细信息
                    sample = refined_synthetic_data[0]
                    rel_id = sample['relation']
                    rel_name = [name for name, id in rel2id.items() if id == rel_id][0] if rel_id in [rel2id[r] for r in prev_relations] else "未知关系"

                    print("\n第一个伪样本详细信息:")
                    print(f"关系名称: {rel_name}")
                    print(f"关系ID: {sample['relation']}")
                    print(f"负样本标签: {sample['neg_labels']}")
                    print(f"Token长度: {len(sample['tokens'])}")

                    # 解码tokens
                    tokenizer = BertTokenizer.from_pretrained(config.bert_path,
                                                             additional_special_tokens=["[E11]", "[E12]", "[E21]", "[E22]"])
                    decoded = tokenizer.decode(sample['tokens'], skip_special_tokens=False)
                    print(f"解码后的文本: {decoded[:100]}...")
                    print("-"*50)

            print("\n【开始数据扩展】")
            print(f"原始训练数据: {len(train_data_for_initial)} 个样本")
            print(f"原始历史数据: {len(prev_samples)} 个样本")

            expanded_train_data_for_initial, expanded_prev_samples = data_augmentation(config, encoder,
                                                                                       train_data_for_initial,
                                                                                       prev_samples)
            torch.cuda.empty_cache()

            print(f"【数据扩展结果】")
            print(f"扩展后训练数据: {len(expanded_train_data_for_initial)} 个样本 (增加了 {len(expanded_train_data_for_initial) - len(train_data_for_initial)} 个)")
            print(f"扩展后历史数据: {len(expanded_prev_samples)} 个样本 (增加了 {len(expanded_prev_samples) - len(prev_samples)} 个)")


            print("\n【开始第一阶段记忆模型训练】")
            print(f"训练数据数量: {len(train_data_for_initial)} 个样本")
            print(f"训练轮数: {config.step2_epochs} 轮")
            train_mem_model(config, encoder, dropout_layer, classifier, train_data_for_initial, config.step2_epochs, map_relid2tempid, new_relation_data,
                        prev_encoder, prev_dropout_layer, prev_classifier, prev_relation_index)
            print(f"【第一阶段记忆模型训练完成】")

            print("\n【开始选择记忆样本】")
            for relation in current_relations:
                print(f"处理关系: '{relation}'")
                selected_samples = select_data(config, encoder, dropout_layer, training_data[relation])
                memorized_samples[relation] = selected_samples
                memory[rel2id[relation]] = selected_samples
                print(f"  - 原始样本数: {len(training_data[relation])}")
                print(f"  - 选择样本数: {len(selected_samples)}")
            print("【记忆样本选择完成】")

            print("\n【准备第二阶段训练数据】")
            train_data_for_memory = []
            # train_data_for_memory += expanded_prev_samples
            train_data_for_memory += prev_samples
            print(f"添加历史样本: {len(prev_samples)} 个")

            current_samples_count = 0
            for relation in current_relations:
                relation_samples = memorized_samples[relation]
                train_data_for_memory += relation_samples
                current_samples_count += len(relation_samples)

            print(f"添加当前任务样本: {current_samples_count} 个")

            # 添加SSR生成的伪样本
            if 'synthetic_samples_for_memory' in locals() and synthetic_samples_for_memory:
                # 过滤掉关系ID不在map_relid2tempid中的样本
                valid_synthetic_samples = []
                invalid_count = 0
                for sample in synthetic_samples_for_memory:
                    if sample['relation'] in map_relid2tempid:
                        valid_synthetic_samples.append(sample)
                    else:
                        invalid_count += 1

                train_data_for_memory.extend(valid_synthetic_samples)
                print(f"添加SSR伪样本: {len(valid_synthetic_samples)} 个 (过滤了 {invalid_count} 个无效关系ID的样本)")

            print(f"总训练样本数: {len(train_data_for_memory)} 个")
            print(f"已学习关系数: {len(seen_relations)} 个")

            temp_protos = {}
            for relation in seen_relations:
                proto, _ = get_proto(config, encoder, dropout_layer, memorized_samples[relation])
                temp_protos[rel2id[relation]] = proto

            print("\n【开始第二阶段记忆模型训练】")
            print(f"训练数据数量: {len(train_data_for_memory)} 个样本")
            print(f"训练轮数: {config.step3_epochs} 轮")
            train_mem_model(config, encoder, dropout_layer, classifier, train_data_for_memory, config.step3_epochs, map_relid2tempid, new_relation_data,
                        prev_encoder, prev_dropout_layer, prev_classifier, prev_relation_index)
            print(f"【第二阶段记忆模型训练完成】")
            print("\n【准备测试数据】")
            # 准备当前任务测试数据
            test_data_1 = []
            for relation in current_relations:
                test_data_1 += test_data[relation]
            print(f"当前任务测试样本: {len(test_data_1)} 个")

            # 准备历史任务测试数据
            test_data_2 = []
            for relation in seen_relations:
                relation_test_data = historic_test_data[relation]
                test_data_2 += relation_test_data
                print(f"  - 关系 '{relation}' 测试样本: {len(relation_test_data)} 个")
            print(f"历史任务测试样本总数: {len(test_data_2)} 个")

            # 保存当前任务测试数据用于后续评估
            history_data.append(test_data_1)
            # cur_acc = evaluate_strict_model(config, encoder, classifier, test_data_1, seen_relations, map_relid2tempid)
            # total_acc = evaluate_strict_model(config, encoder, classifier, test_data_2, seen_relations, map_relid2tempid)

            cur_acc = evaluate_strict_model(config, encoder,dropout_layer,classifier, test_data_1, seen_relations, map_relid2tempid)
            total_acc = evaluate_strict_model(config, encoder, dropout_layer, classifier, test_data_2, seen_relations, map_relid2tempid)

            print("\n" + "="*80)
            print(f'【评估结果】第 {rou + 1} 轮，第 {steps + 1} 步')
            print("-"*50)
            print(f'【当前任务准确率】{cur_acc:.4f} ({cur_acc:.2%})')
            print(f'【历史任务准确率】{total_acc:.4f} ({total_acc:.2%})')

            test_cur.append(cur_acc)
            test_total.append(total_acc)

            print("\n【准确率历史记录】")
            print(f"当前任务准确率历史: {[f'{acc:.4f}' for acc in test_cur]}")
            print(f"历史任务准确率历史: {[f'{acc:.4f}' for acc in test_total]}")
            print("="*80)
            accuracy = []
            temp_rel2id = [rel2id[x] for x in history_relations]
            map_relid2tempid = {k: v for v, k in enumerate(temp_rel2id)}
            for data in history_data:
                # accuracy.append(
                #     evaluate_strict_model(config, encoder, classifier, data, history_relations, map_relid2tempid))
                accuracy.append(evaluate_strict_model(config, encoder, dropout_layer, classifier, data, seen_relations, map_relid2tempid))
            print(accuracy)

            prev_encoder = deepcopy(encoder)
            prev_dropout_layer = deepcopy(dropout_layer)
            prev_classifier = deepcopy(classifier)
            torch.cuda.empty_cache()

            # 保存检查点
            if steps % args.save_interval == 0:
                # 创建检查点状态
                checkpoint_state = {
                    'round': rou,
                    'step': steps + 1,  # 保存下一步的索引
                    'encoder_state_dict': encoder.state_dict(),
                    'dropout_layer_state_dict': dropout_layer.state_dict(),
                    'classifier_state_dict': classifier.state_dict() if classifier else None,
                    'history_relations': history_relations,
                    'memorized_samples': memorized_samples,
                    'relation_standard': relation_standard,
                    'test_cur': test_cur,
                    'test_total': test_total,
                    'result_cur_test': result_cur_test,
                    'result_whole_test': result_whole_test,
                    'bwt_whole': bwt_whole,
                    'fwt_whole': fwt_whole,
                    'relation_divides': relation_divides,
                    'history_data': history_data,
                    'config': {
                        'task': config.task,
                        'shot': config.shot,
                        'device': str(config.device),
                        'seed': config.seed
                    },
                    'timestamp': time.time()
                }

                # 保存检查点
                checkpoint_path = save_checkpoint(config, checkpoint_state, checkpoint_dir=args.checkpoint_dir)

                # 如果需要只保留最新检查点，则删除其他检查点
                if args.keep_latest_only:
                    from checkpoint_manager import clean_checkpoints
                    print("清理旧检查点，只保留最新的检查点...")
                    clean_checkpoints(args.checkpoint_dir, keep_latest=True)
        result_cur_test.append(np.array(test_cur))
        result_whole_test.append(np.array(test_total)*100)
        print("result_whole_test")
        print(result_whole_test)
        avg_result_cur_test = np.average(result_cur_test, 0)
        avg_result_all_test = np.average(result_whole_test, 0)
        print("avg_result_cur_test")
        print(avg_result_cur_test)
        print("avg_result_all_test")
        print(avg_result_all_test)
        std_result_all_test = np.std(result_whole_test, 0)
        print("std_result_all_test")
        print(std_result_all_test)

        accuracy = []
        temp_rel2id = [rel2id[x] for x in history_relations]
        map_relid2tempid = {k: v for v, k in enumerate(temp_rel2id)}
        for data in history_data:
            accuracy.append(evaluate_strict_model(config, encoder, dropout_layer, classifier, data, history_relations, map_relid2tempid))
        print(accuracy)
        bwt = 0.0
        for k in range(len(accuracy)-1):
            bwt += accuracy[k]-test_cur[k]
        bwt /= len(accuracy)-1
        bwt_whole.append(bwt)
        fwt_whole.append(np.average(np.array(forward_accs)))
        print("bwt_whole")
        print(bwt_whole)
        print("fwt_whole")
        print(fwt_whole)
        avg_bwt = np.average(np.array(bwt_whole))
        print("avg_bwt_whole")
        print(avg_bwt)
        avg_fwt = np.average(np.array(fwt_whole))
        print("avg_fwt_whole")
        print(avg_fwt)


