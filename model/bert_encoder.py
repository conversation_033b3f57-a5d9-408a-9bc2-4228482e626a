import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from model.base_model import base_model
from transformers import BertModel, BertConfig


class Bert_Encoder(base_model):

    def __init__(self, config):
        super(<PERSON>_Encoder, self).__init__()

        # load model
        self.encoder = BertModel.from_pretrained(config.bert_path).to(config.device)
        self.bert_config = BertConfig.from_pretrained(config.bert_path)

        # the dimension for the final outputs
        self.output_size = config.encoder_output_size
        self.drop = nn.Dropout(config.drop_out)

        # find which encoding is used
        if config.pattern in ['standard', 'entity_marker']:
            self.pattern = config.pattern
        else:
            raise Exception('Wrong encoding.')
        config.hidden_size = self.bert_config.hidden_size
        config.output_size = config.encoder_output_size
        if self.pattern == 'entity_marker':
            self.encoder.resize_token_embeddings(config.vocab_size + config.marker_size)
            self.linear_transform = nn.Linear(self.bert_config.hidden_size * 2, self.output_size, bias=True)
        else:
            self.linear_transform = nn.Linear(self.bert_config.hidden_size, self.output_size, bias=True)

        self.layer_normalization = nn.LayerNorm([self.output_size])

    def get_output_size(self):
        return self.output_size

    def forward(self, inputs):
        '''
        :param inputs: of dimension [B, N]
        :return: a result of size [B, H*2] or [B, H], according to different strategy
        '''
        # generate representation under a certain encoding strategy
        if self.pattern == 'standard':
            # in the standard mode, the representation is generated according to
            #  the representation of[CLS] mark.
            output = self.encoder(inputs)[1]
        else:
            # in the entity_marker mode, the representation is generated from the representations of
            #  marks [E11] and [E21] of the head and tail entities.
            e11 = []
            e21 = []
            valid_indices = []
            # for each sample in the batch, acquire the positions of its [E11] and [E21]
            for i in range(inputs.size()[0]):
                tokens = inputs[i].cpu().numpy()
                # 检查是否包含必要的特殊标记
                e11_positions = np.argwhere(tokens == 30522)
                e21_positions = np.argwhere(tokens == 30524)

                if len(e11_positions) > 0 and len(e21_positions) > 0:
                    e11.append(e11_positions[0][0])
                    e21.append(e21_positions[0][0])
                    valid_indices.append(i)

            # 如果没有有效样本，返回空张量
            if not valid_indices:
                return torch.zeros(inputs.size()[0], self.output_size, device=inputs.device)

            # input the sample to BERT
            tokens_output = self.encoder(inputs)[0] # [B,N] --> [B,N,H]
            output = []

            # for each sample in the batch, acquire its representations for [E11] and [E21]
            for idx, i in enumerate(valid_indices):
                instance_output = torch.index_select(tokens_output, 0, torch.tensor(i, device=tokens_output.device))
                instance_output = torch.index_select(instance_output, 1, torch.tensor([e11[idx], e21[idx]], device=tokens_output.device))
                output.append(instance_output)  # [B,N] --> [B,2,H]

            # for each sample in the batch, concatenate the representations of [E11] and [E21], and reshape
            if output:
                output = torch.cat(output, dim=0)
                output = output.view(output.size()[0], -1)  # [B,N] --> [B,H*2]

                # 如果有效样本数量少于输入样本数量，填充结果
                if len(valid_indices) < inputs.size()[0]:
                    # 创建一个全零张量
                    full_output = torch.zeros(inputs.size()[0], output.size()[1], device=output.device)
                    # 将有效样本的输出复制到对应位置
                    for idx, i in enumerate(valid_indices):
                        if idx < output.size()[0]:
                            full_output[i] = output[idx]
                    output = full_output
            else:
                # 如果没有有效输出，返回全零张量
                output = torch.zeros(inputs.size()[0], self.output_size, device=inputs.device)

            # the output dimension is [B, H*2], B: batchsize, H: hiddensize
            # output = self.drop(output)
            # output = self.linear_transform(output)
            # output = F.gelu(output)
            # output = self.layer_normalization(output)
        return output