import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.autograd import Function
from transformers import BertModel, BertPreTrainedModel

# 梯度反转层
class GradientReversalFunction(Function):
    @staticmethod
    def forward(ctx, x, lambda_):
        ctx.lambda_ = lambda_
        return x.clone()

    @staticmethod
    def backward(ctx, grads):
        lambda_ = ctx.lambda_
        dx = -lambda_ * grads
        return dx, None

class GradientReversalLayer(nn.Module):
    def __init__(self, lambda_=0.5):
        super(GradientReversalLayer, self).__init__()
        self.lambda_ = lambda_

    def forward(self, x):
        return GradientReversalFunction.apply(x, self.lambda_)

# 特征蒸馏模块
class ContextDistillation(nn.Module):
    def __init__(self, input_dim, hidden_dim):
        super(ContextDistillation, self).__init__()
        self.fc = nn.Linear(hidden_dim, input_dim)  # 全连接层
        self.grl = GradientReversalLayer()  # 梯度反转层

    def forward(self, sentence_embeddings, common_emb):
        # 使用梯度反转层进行特征蒸馏
        irrelevant_proj = self.fc(common_emb)
        irrelevant_proj = self.grl(irrelevant_proj)
        refined_context = sentence_embeddings - irrelevant_proj
        return refined_context

# class DistillationRelationExtraction(nn.Module):
#     def __init__(self, entity_dim, context_dim, num_labels, hidden_dim, lambda_=0.5):
#         super(DistillationRelationExtraction, self).__init__()
#         self.entity_fc = nn.Linear(entity_dim, num_labels)  # 实体特征全连接层
#         self.context_fc = nn.Linear(context_dim, num_labels)  # 上下文特征全连接层
#         self.distillation = ContextDistillation(context_dim, hidden_dim)  # 特征蒸馏模块
#         self.loss_fn = nn.CrossEntropyLoss()  # 损失函数
#         self.fclayer = nn.Linear(context_dim * 2, context_dim)  # 用于句子嵌入的全连接层

#     def extract_entity(self, sequence_output, e_mask):
#         extended_e_mask = e_mask.unsqueeze(-1)
#         extended_e_mask = extended_e_mask.float() * sequence_output
#         extended_e_mask, _ = extended_e_mask.max(dim=-2)
#         return extended_e_mask.float()

#     def forward(self, sequence_output, sentence_embeddings, common_emb, labels):
#         # 在类内部直接计算 e1_h 和 e2_h
#         marked_head = None  # 这里应该直接从 sequence_output 或相关数据中提取
#         marked_tail = None  # 这里应该直接从 sequence_output 或相关数据中提取

#         e1_h = self.extract_entity(sequence_output, marked_head)
#         e2_h = self.extract_entity(sequence_output, marked_tail)

#         # 实体匹配
#         entity_logits = self.entity_fc(torch.cat([e1_h, e2_h], dim=-1))
        
#         # 上下文匹配并应用特征蒸馏
#         refined_context = self.distillation(sentence_embeddings, common_emb)
#         context_logits = self.context_fc(refined_context)
        
#         # 最终输出
#         logits = entity_logits + context_logits
        
#         # 计算损失并返回
#         loss = self.loss_fn(logits, labels)
        
#         return loss
class DistillationRelationExtraction(nn.Module):
    def __init__(self, entity_dim, context_dim, num_labels, hidden_dim, lambda_=0.5, prev_encoder=None):
        super(DistillationRelationExtraction, self).__init__()
        self.entity_fc = nn.Linear(entity_dim, num_labels)  # 实体特征全连接层
        self.context_fc = nn.Linear(context_dim, num_labels)  # 上下文特征全连接层
        self.distillation = ContextDistillation(context_dim, hidden_dim)  # 特征蒸馏模块
        self.loss_fn = nn.CrossEntropyLoss()  # 损失函数
        self.prev_encoder = prev_encoder  # 前一个编码器

    def forward(self, tokens, sentence_embeddings, common_emb, labels):
        if self.prev_encoder is not None:
            # 如果 prev_encoder 存在，生成 prev_reps
            prev_reps = self.prev_encoder(tokens).detach()
            normalized_prev_reps_emb = F.normalize(prev_reps.view(-1, prev_reps.size()[1]), p=2, dim=1)
        else:
            # 如果 prev_encoder 不存在，使用一个与 sentence_embeddings 大小相同的零张量代替
            normalized_prev_reps_emb = torch.zeros_like(sentence_embeddings).to(sentence_embeddings.device)

        # 上下文匹配并应用特征蒸馏
        refined_context = self.distillation(sentence_embeddings, common_emb)
        context_logits = self.context_fc(refined_context)

        # 计算损失
        loss = self.loss_fn(context_logits, labels)
        return loss

