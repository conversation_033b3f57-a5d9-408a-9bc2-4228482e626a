import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.autograd import Function

# 梯度反转层
class GradientReversalFunction(Function):
    @staticmethod
    def forward(ctx, x, lambda_):
        ctx.lambda_ = lambda_
        return x.clone()

    @staticmethod
    def backward(ctx, grads):
        lambda_ = ctx.lambda_
        dx = -lambda_ * grads
        return dx, None

class GradientReversalLayer(nn.Module):
    def __init__(self, lambda_=0.5):
        super(GradientReversalLayer, self).__init__()
        self.lambda_ = lambda_

    def forward(self, x):
        return GradientReversalFunction.apply(x, self.lambda_)

# 特征蒸馏模块
class ContextDistillation(nn.Module):
    def __init__(self, input_dim, hidden_dim):
        super(ContextDistillation, self).__init__()
        self.fc = nn.Linear(hidden_dim, input_dim)  # 全连接层
        self.grl = GradientReversalLayer()  # 梯度反转层

    def forward(self, sentence_embeddings, common_emb):
        # 使用梯度反转层进行特征蒸馏
        irrelevant_proj = self.fc(common_emb)
        irrelevant_proj = self.grl(irrelevant_proj)
        refined_context = sentence_embeddings - irrelevant_proj
        return refined_context

# 关系抽取模型
class RelationExtractionModel(nn.Module):
    def __init__(self, entity_dim, context_dim, num_labels, hidden_dim, lambda_=0.5):
        super(RelationExtractionModel, self).__init__()
        self.entity_fc = nn.Linear(entity_dim, num_labels)  # 实体特征全连接层
        self.context_fc = nn.Linear(context_dim, num_labels)  # 上下文特征全连接层
        self.distillation = ContextDistillation(context_dim, hidden_dim)  # 特征蒸馏模块
        self.loss_fn = nn.CrossEntropyLoss()  # 损失函数

    def forward(self, e1_h, e2_h, sentence_embeddings, common_emb, labels):
        # 实体匹配
        entity_logits = self.entity_fc(torch.cat([e1_h, e2_h], dim=-1))
        
        # 上下文匹配并应用特征蒸馏
        refined_context = self.distillation(sentence_embeddings, common_emb)
        context_logits = self.context_fc(refined_context)
        
        # 最终输出
        logits = entity_logits + context_logits
        
        # 计算损失并返回
        loss = self.loss_fn(logits, labels)
        
        return loss
