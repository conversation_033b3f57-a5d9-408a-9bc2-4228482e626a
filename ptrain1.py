def train_mem_model(config, encoder, dropout_layer, classifier, training_data, epochs, map_relid2tempid, new_relation_data,
                    prev_encoder, prev_dropout_layer, prev_classifier, prev_relation_index):
    data_loader = get_data_loader(config, training_data, shuffle=True)

    encoder.train()
    dropout_layer.train()
    classifier.train()

    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam([
        {'params': encoder.parameters(), 'lr': 0.00001},
        {'params': dropout_layer.parameters(), 'lr': 0.00001},
        {'params': classifier.parameters(), 'lr': 0.001}
    ])
    triplet_loss = nn.TripletMarginLoss(margin=1.0, p=2)
    distill_criterion = nn.CosineEmbeddingLoss()

    # 实例化HistillationRelationExtraction类
    distillation_model = HistillationRelationExtraction(
        entity_dim=config.encoder_output_size,  # 使用config中的encoder_output_size
        context_dim=config.encoder_output_size,  # 假设context和entity使用相同的维度
        num_labels=len(prev_relation_index),    # 标签数量
        hidden_dim=config.encoder_output_size,  # 使用相同的维度
        lambda_=config.kl_temp  # 使用配置中的温度参数
    ).to(config.device)

    T = config.kl_temp
    for epoch_i in range(epochs):
        losses = []
        for step, (labels, _, tokens) in enumerate(data_loader):

            optimizer.zero_grad()

            logits_all = []
            tokens = torch.stack([x.to(config.device) for x in tokens], dim=0)
            labels = labels.to(config.device)
            origin_labels = labels[:]
            labels = [map_relid2tempid[x.item()] for x in labels]
            labels = torch.tensor(labels).to(config.device)
            reps = encoder(tokens)
            normalized_reps_emb = F.normalize(reps.view(-1, reps.size()[1]), p=2, dim=1)
            outputs, _ = dropout_layer(reps)
            if prev_dropout_layer is not None:
                prev_outputs, _ = prev_dropout_layer(reps)
                positives, negatives = construct_hard_triplets(prev_outputs, origin_labels, new_relation_data)
            else:
                positives, negatives = construct_hard_triplets(outputs, origin_labels, new_relation_data)

            for _ in range(config.f_pass):
                output, output_embedding = dropout_layer(reps)
                logits = classifier(output)
                logits_all.append(logits)

            positives = torch.cat(positives, 0).to(config.device)
            negatives = torch.cat(negatives, 0).to(config.device)
            anchors = outputs
            logits_all = torch.stack(logits_all)
            m_labels = labels.expand((config.f_pass, labels.shape[0]))  # m,B
            loss1 = criterion(logits_all.reshape(-1, logits_all.shape[-1]), m_labels.reshape(-1))
            loss2 = compute_jsd_loss(logits_all)
            tri_loss = triplet_loss(anchors, positives, negatives)
            loss = loss1 + loss2 + tri_loss

            if prev_encoder is not None:
                prev_reps = prev_encoder(tokens).detach()
                normalized_prev_reps_emb = F.normalize(prev_reps.view(-1, prev_reps.size()[1]), p=2, dim=1)

                if prev_dropout_layer is not None and prev_classifier is not None:
                    dropout_output_all = []
                    prev_dropout_output_all = []
                    for i in range(config.f_pass):
                        output, _ = dropout_layer(reps)
                        prev_output, _ = prev_dropout_layer(reps)
                        dropout_output_all.append(output)
                        prev_dropout_output_all.append(prev_output)
                    
                    # 使用HistillationRelationExtraction计算hidden_distill_loss
                    hidden_distill_loss = distillation_model(
                        entity_features=normalized_reps_emb,
                        context_features=normalized_reps_emb,  # 这里假设context_features和entity_features相同
                        irrelevant_features=torch.zeros_like(normalized_reps_emb),  # 假设irrelevant_features为零张量
                        labels=labels
                    )
                    loss += hidden_distill_loss

            loss.backward()
            losses.append(loss.item())
            optimizer.step()
        print(f"loss is {np.array(losses).mean()}")
