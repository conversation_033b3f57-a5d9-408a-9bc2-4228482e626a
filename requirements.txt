certifi==2022.12.7
charset-normalizer==2.1.1
click==8.1.7
decorator==5.1.1
filelock==3.13.4
fsspec==2024.3.1
huggingface-hub==0.22.2
idna==3.4
joblib==1.4.0
nltk==3.8.1
numpy==1.24.1
opt-einsum==3.3.0
packaging==24.0
pillow==10.2.0
pip==23.3.1
py==1.11.0
PyYAML==6.0.1
regex==2024.4.16
requests==2.28.1
retry==0.9.2
safetensors==0.4.3
scikit-learn==1.3.2
scipy==1.5.2
sentence-transformers==2.2.2
sentencepiece==0.2.0
setuptools==68.2.2
threadpoolctl==3.4.0
tokenizers==0.15.2
# torch相关包需要从PyTorch官方源安装
# 使用以下命令安装:
# pip install torch==1.11.0+cu102 torchvision==0.12.0+cu102 torchaudio==0.11.0+cu102 -f https://download.pytorch.org/whl/torch_stable.html
tqdm==4.66.2
transformers==4.35.2
typing_extensions==4.8.0
ujson==5.9.0
urllib3==1.26.13
wheel==0.41.2