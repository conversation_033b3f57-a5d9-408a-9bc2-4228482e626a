#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
重新启动SCKD项目，删除旧检查点并从头开始训练
"""

import os
import sys
import argparse
import subprocess
from checkpoint_manager import clean_checkpoints

def parse_args():
    parser = argparse.ArgumentParser(description="重新启动SCKD项目，删除旧检查点")
    parser.add_argument("--checkpoint_dir", default="checkpoints", help="检查点目录")
    parser.add_argument("--keep_latest", action="store_true", help="是否保留最新的检查点")
    parser.add_argument("--main_script", default="main.py", help="主脚本文件名")
    parser.add_argument("--args", default="", help="传递给主脚本的参数")
    return parser.parse_args()

def main():
    args = parse_args()
    
    # 检查检查点目录是否存在
    if not os.path.exists(args.checkpoint_dir):
        print(f"检查点目录 {args.checkpoint_dir} 不存在，将创建该目录")
        os.makedirs(args.checkpoint_dir)
    
    # 清理检查点
    print("开始清理检查点...")
    clean_checkpoints(args.checkpoint_dir, args.keep_latest)
    
    # 构建启动命令
    cmd = [sys.executable, args.main_script]
    
    # 添加不从检查点恢复的参数
    if "--no_resume" not in args.args and "--resume" not in args.args:
        cmd.append("--no_resume")
    
    # 添加其他参数
    if args.args:
        cmd.extend(args.args.split())
    
    # 启动主脚本
    print(f"启动命令: {' '.join(cmd)}")
    print("重新启动项目...")
    
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"启动项目时出错: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("用户中断执行")
        sys.exit(1)

if __name__ == "__main__":
    main()
