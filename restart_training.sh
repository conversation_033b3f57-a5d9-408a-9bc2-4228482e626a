#!/bin/bash

# 设置默认参数
CHECKPOINT_DIR="checkpoints"
KEEP_LATEST=false
TASK="tacred"
SHOT=10
CONFIG="config.ini"
SAVE_INTERVAL=1

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  case $1 in
    --checkpoint_dir)
      CHECKPOINT_DIR="$2"
      shift 2
      ;;
    --keep_latest)
      KEEP_LATEST=true
      shift
      ;;
    --task)
      TASK="$2"
      shift 2
      ;;
    --shot)
      SHOT="$2"
      shift 2
      ;;
    --config)
      CONFIG="$2"
      shift 2
      ;;
    --save_interval)
      SAVE_INTERVAL="$2"
      shift 2
      ;;
    *)
      echo "未知参数: $1"
      exit 1
      ;;
  esac
done

# 显示参数
echo "使用以下参数重新启动训练:"
echo "检查点目录: $CHECKPOINT_DIR"
echo "保留最新检查点: $KEEP_LATEST"
echo "任务: $TASK"
echo "样本数: $SHOT"
echo "配置文件: $CONFIG"
echo "保存间隔: $SAVE_INTERVAL"

# 确认操作
read -p "确定要删除检查点并重新开始训练吗? (y/n) " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
  echo "操作已取消"
  exit 0
fi

# 清理检查点目录
echo "清理检查点目录..."
if [ "$KEEP_LATEST" = true ]; then
  # 使用Python脚本清理检查点，保留最新的
  python -c "from checkpoint_manager import clean_checkpoints; clean_checkpoints('$CHECKPOINT_DIR', True)"
else
  # 删除所有检查点
  rm -rf "$CHECKPOINT_DIR"/*
  mkdir -p "$CHECKPOINT_DIR"
fi

# 构建启动命令
CMD="python main.py --task $TASK --shot $SHOT --config $CONFIG --checkpoint_dir $CHECKPOINT_DIR --save_interval $SAVE_INTERVAL --no_resume"

if [ "$KEEP_LATEST" = true ]; then
  CMD="$CMD --keep_latest_only"
fi

# 启动训练
echo "启动命令: $CMD"
echo "开始训练..."
eval $CMD
