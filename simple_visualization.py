#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import torch
import numpy as np
import matplotlib.pyplot as plt
import argparse
import pandas as pd
from matplotlib.ticker import MaxNLocator

def load_checkpoint_data(checkpoint_dir='checkpoints'):
    """
    直接从检查点加载数据
    
    Args:
        checkpoint_dir: 检查点目录
        
    Returns:
        包含所有检查点数据的DataFrame
    """
    data = []
    
    # 获取所有检查点文件
    checkpoint_files = [f for f in os.listdir(checkpoint_dir) if f.endswith('.pt')]
    checkpoint_files.sort()  # 按文件名排序
    
    for checkpoint_file in checkpoint_files:
        # 解析文件名以获取轮次和步骤信息
        parts = checkpoint_file.split('_')
        task = parts[1]
        shot = int(parts[2])
        round_num = int(parts[3].replace('round', ''))
        step_num = int(parts[4].replace('step', ''))
        
        # 加载检查点
        checkpoint_path = os.path.join(checkpoint_dir, checkpoint_file)
        try:
            checkpoint = torch.load(checkpoint_path, map_location=lambda storage, loc: storage)
            
            # 创建数据行
            row = {
                'task': task,
                'shot': shot,
                'round': round_num,
                'step': step_num,
                'position': round_num + step_num / 10,  # 用于绘图的位置
                'timestamp': checkpoint.get('timestamp', None)
            }
            
            # 提取性能指标
            if 'result_whole_test' in checkpoint:
                whole_test = checkpoint['result_whole_test']
                if isinstance(whole_test, list) and len(whole_test) > 0:
                    if isinstance(whole_test[-1], (list, np.ndarray)) and len(whole_test[-1]) > step_num - 1:
                        row['whole_test_acc'] = float(whole_test[-1][step_num - 1])
            
            if 'result_cur_test' in checkpoint:
                cur_test = checkpoint['result_cur_test']
                if isinstance(cur_test, list) and len(cur_test) > 0:
                    if isinstance(cur_test[-1], (list, np.ndarray)) and len(cur_test[-1]) > step_num - 1:
                        row['cur_test_acc'] = float(cur_test[-1][step_num - 1])
            
            if 'bwt_whole' in checkpoint:
                bwt = checkpoint['bwt_whole']
                if isinstance(bwt, list) and len(bwt) > 0:
                    row['bwt'] = float(bwt[-1])
            
            if 'fwt_whole' in checkpoint:
                fwt = checkpoint['fwt_whole']
                if isinstance(fwt, list) and len(fwt) > 0:
                    row['fwt'] = float(fwt[-1])
            
            # 添加到数据列表
            data.append(row)
            print(f"已加载检查点 {checkpoint_file} 的数据")
        except Exception as e:
            print(f"加载检查点 {checkpoint_file} 时出错: {e}")
    
    # 创建DataFrame
    df = pd.DataFrame(data)
    return df

def plot_learning_curve(df, output_dir):
    """绘制学习曲线"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 设置图表样式
    plt.figure(figsize=(14, 10))
    
    # 绘制整体测试准确率
    plt.subplot(2, 1, 1)
    if 'whole_test_acc' in df.columns:
        plt.plot(df['position'], df['whole_test_acc'], 'o-', label='Whole Test Accuracy')
    if 'cur_test_acc' in df.columns:
        plt.plot(df['position'], df['cur_test_acc'], 's-', label='Current Test Accuracy')
    
    plt.title('Accuracy Metrics Over Training')
    plt.xlabel('Training Progress (Round.Step)')
    plt.ylabel('Accuracy (%)')
    plt.legend()
    plt.grid(True)
    
    # 绘制BWT和FWT
    plt.subplot(2, 1, 2)
    if 'bwt' in df.columns:
        plt.plot(df['position'], df['bwt'], 'o-', label='BWT')
    if 'fwt' in df.columns:
        plt.plot(df['position'], df['fwt'], 's-', label='FWT')
    
    plt.title('Transfer Metrics Over Training')
    plt.xlabel('Training Progress (Round.Step)')
    plt.ylabel('Transfer Score')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'learning_curve.png'), dpi=300)
    plt.close()

def plot_round_comparison(df, output_dir):
    """比较不同轮次的性能"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 设置图表样式
    plt.figure(figsize=(14, 8))
    
    # 按轮次分组
    rounds = sorted(df['round'].unique())
    
    # 绘制每轮的整体测试准确率
    for r in rounds:
        round_data = df[df['round'] == r]
        if 'whole_test_acc' in round_data.columns:
            plt.plot(round_data['step'], round_data['whole_test_acc'], 
                     marker='o', label=f'Round {r}')
    
    plt.title('Whole Test Accuracy by Round')
    plt.xlabel('Step')
    plt.ylabel('Accuracy (%)')
    plt.legend()
    plt.grid(True)
    
    # 设置x轴刻度为整数
    ax = plt.gca()
    ax.xaxis.set_major_locator(MaxNLocator(integer=True))
    
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'round_comparison.png'), dpi=300)
    plt.close()

def plot_heatmap(df, output_dir):
    """绘制热力图"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 创建轮次-步骤网格
    if 'whole_test_acc' in df.columns:
        pivot_table = df.pivot_table(index='round', columns='step', 
                                     values='whole_test_acc', aggfunc='mean')
        
        # 绘制热力图
        plt.figure(figsize=(10, 8))
        plt.imshow(pivot_table.values, cmap='YlGnBu', interpolation='nearest')
        
        # 添加颜色条
        plt.colorbar(label='Accuracy (%)')
        
        # 设置坐标轴
        plt.xticks(range(len(pivot_table.columns)), pivot_table.columns)
        plt.yticks(range(len(pivot_table.index)), pivot_table.index)
        plt.xlabel('Step')
        plt.ylabel('Round')
        
        # 添加数值标签
        for i in range(len(pivot_table.index)):
            for j in range(len(pivot_table.columns)):
                plt.text(j, i, f"{pivot_table.values[i, j]:.2f}", 
                         ha="center", va="center", color="black")
        
        plt.title('Whole Test Accuracy Heatmap (Round vs Step)')
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'accuracy_heatmap.png'), dpi=300)
        plt.close()

def plot_forgetting_analysis(df, output_dir):
    """分析遗忘现象"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    if 'bwt' in df.columns:
        # 设置图表样式
        plt.figure(figsize=(12, 6))
        
        # 按步骤分组，查看每个步骤的BWT变化
        steps = sorted(df['step'].unique())
        
        for s in steps:
            step_data = df[df['step'] == s]
            plt.plot(step_data['round'], step_data['bwt'], 
                     marker='o', label=f'Step {s}')
        
        plt.title('Backward Transfer (BWT) by Round for Each Step')
        plt.xlabel('Round')
        plt.ylabel('BWT Score')
        plt.legend()
        plt.grid(True)
        
        # 设置x轴刻度为整数
        ax = plt.gca()
        ax.xaxis.set_major_locator(MaxNLocator(integer=True))
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'forgetting_analysis.png'), dpi=300)
        plt.close()

def create_summary_report(df, output_dir):
    """创建摘要报告"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 计算整体统计数据
    summary = {}
    
    for metric in ['whole_test_acc', 'cur_test_acc', 'bwt', 'fwt']:
        if metric in df.columns:
            summary[metric] = {
                'mean': df[metric].mean(),
                'std': df[metric].std(),
                'min': df[metric].min(),
                'max': df[metric].max()
            }
    
    # 保存摘要报告为CSV
    summary_rows = []
    for metric, stats in summary.items():
        summary_rows.append({
            'Metric': metric,
            'Mean': stats['mean'],
            'Std': stats['std'],
            'Min': stats['min'],
            'Max': stats['max']
        })
    
    summary_df = pd.DataFrame(summary_rows)
    summary_df.to_csv(os.path.join(output_dir, 'summary_report.csv'), index=False)
    
    # 打印摘要
    print("\n摘要统计:")
    print(summary_df)

def main():
    parser = argparse.ArgumentParser(description='简单可视化SCKD实验结果')
    parser.add_argument('--checkpoint_dir', type=str, default='checkpoints',
                        help='检查点目录路径')
    parser.add_argument('--output_dir', type=str, default='visualizations',
                        help='可视化输出目录路径')
    args = parser.parse_args()
    
    print(f"从 {args.checkpoint_dir} 加载检查点数据...")
    df = load_checkpoint_data(args.checkpoint_dir)
    
    if df.empty:
        print("没有找到有效的结果数据。请确保检查点包含结果信息。")
        return
    
    print(f"生成可视化结果到 {args.output_dir}...")
    plot_learning_curve(df, args.output_dir)
    plot_round_comparison(df, args.output_dir)
    plot_heatmap(df, args.output_dir)
    plot_forgetting_analysis(df, args.output_dir)
    create_summary_report(df, args.output_dir)
    
    print("可视化完成！")

if __name__ == "__main__":
    main()
