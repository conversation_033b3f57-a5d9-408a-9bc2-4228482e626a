import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import random
from data_loader import get_data_loader
from transformers import BertTokenizer
from copy import deepcopy

class SSR:
    """
    Self-Synthesized Rehearsal (SSR) for continual learning in relation extraction.
    This class implements methods to generate synthetic data for rehearsing previous relations
    when learning new relations.
    """

    def __init__(self, config, encoder, dropout_layer, classifier):
        """
        Initialize the SSR module.

        Args:
            config: Configuration object
            encoder: BERT encoder model
            dropout_layer: Dropout layer
            classifier: Classifier model
        """
        self.config = config
        self.encoder = encoder
        self.dropout_layer = dropout_layer
        self.classifier = classifier
        self.device = config.device
        self.tokenizer = BertTokenizer.from_pretrained(config.bert_path,
                                                      additional_special_tokens=["[E11]", "[E12]", "[E21]", "[E22]"])

        # 从配置文件读取SSR参数
        try:
            # 如果配置文件中有SSR部分，则使用配置文件中的参数
            self.num_synthetic_per_relation = int(getattr(config, 'num_synthetic_per_relation', 20))
            self.diversity_threshold = float(getattr(config, 'diversity_threshold', 0.5))  # 降低多样性阈值
            self.quality_threshold = float(getattr(config, 'quality_threshold', 0.3))  # 大幅降低质量阈值
            self.enable = bool(getattr(config, 'enable', True))
            self.debug = bool(getattr(config, 'debug', False))  # 添加调试模式
        except (AttributeError, ValueError):
            # 如果配置文件中没有SSR部分，则使用默认参数
            self.num_synthetic_per_relation = 20  # 每个关系生成的伪样本数量
            self.diversity_threshold = 0.5  # 降低伪样本多样性阈值
            self.quality_threshold = 0.3  # 降低伪样本质量阈值
            self.enable = True  # 默认启用SSR
            self.debug = False  # 默认不启用调试模式

    def generate_synthetic_data(self, prev_relations, prev_relation_data, rel2id):
        """
        Generate synthetic data for previous relations.

        Args:
            prev_relations: List of previous relation names
            prev_relation_data: Dictionary of previous relation data
            rel2id: Mapping from relation name to relation ID

        Returns:
            List of synthetic data samples
        """
        synthetic_data = []

        if self.debug:
            print(f"【SSR内部】开始为 {len(prev_relations)} 个历史关系生成伪数据")
            print(f"【SSR参数】质量阈值: {self.quality_threshold}, 多样性阈值: {self.diversity_threshold}, 每关系样本数: {self.num_synthetic_per_relation}")
            print(f"【SSR模式】{'启用调试模式' if self.debug else '标准模式'}")

        for relation in prev_relations:
            relation_id = rel2id[relation]
            relation_samples = prev_relation_data[relation]

            # 如果没有样本，则跳过
            if len(relation_samples) < 1:
                if self.debug:
                    print(f"【跳过关系】'{relation}' - 原因：没有样本")
                continue

            # 如果只有1个样本，使用噪声方法生成伪样本
            if len(relation_samples) == 1:
                if self.debug:
                    print(f"【单样本处理】关系 '{relation}' - 只有1个样本，将使用噪声方法生成伪样本")
            else:
                if self.debug:
                    print(f"【多样本处理】关系 '{relation}' - 有 {len(relation_samples)} 个样本，将使用组合方法生成伪样本")

            if self.debug:
                print(f"【开始处理】关系 '{relation}' (ID: {relation_id}) - 目标：生成 {self.num_synthetic_per_relation} 个伪样本")

            # Generate synthetic samples for this relation
            relation_synthetic = self._generate_relation_synthetic(relation_samples, relation_id)

            if self.debug:
                success_rate = len(relation_synthetic) / self.num_synthetic_per_relation * 100
                print(f"【生成结果】关系 '{relation}' - 成功生成 {len(relation_synthetic)}/{self.num_synthetic_per_relation} 个伪样本 (成功率: {success_rate:.1f}%)")

            synthetic_data.extend(relation_synthetic)

        return synthetic_data

    def _generate_relation_synthetic(self, relation_samples, relation_id):
        """
        Generate synthetic samples for a specific relation.

        Args:
            relation_samples: List of samples for the relation
            relation_id: ID of the relation

        Returns:
            List of synthetic samples for the relation
        """
        synthetic_samples = []

        # Get embeddings for all samples in this relation
        embeddings = self._get_sample_embeddings(relation_samples)

        # Generate synthetic samples
        for _ in range(self.num_synthetic_per_relation):
            if len(relation_samples) >= 2:
                # 如果有两个或更多样本，使用组合方法
                idx1, idx2 = random.sample(range(len(relation_samples)), 2)
                sample1 = relation_samples[idx1]
                sample2 = relation_samples[idx2]

                # Create a synthetic sample by combining entities from the two samples
                synthetic_sample = self._create_synthetic_sample(sample1, sample2, relation_id)

                if synthetic_sample:
                    # Check quality of the synthetic sample
                    if self._check_sample_quality(synthetic_sample, embeddings):
                        synthetic_samples.append(synthetic_sample)

            elif len(relation_samples) == 1:
                # 如果只有一个样本，使用噪声方法生成伪样本
                if self.debug:
                    print(f"【噪声生成】为关系 ID {relation_id} 生成第 {_+1}/{self.num_synthetic_per_relation} 个伪样本")

                sample = relation_samples[0]

                # 尝试多次生成，增加成功率
                max_attempts = 5  # 每个目标样本尝试5次
                success = False

                for attempt in range(max_attempts):
                    # 创建带噪声的伪样本
                    synthetic_sample = self._create_noisy_sample(sample, relation_id)

                    if synthetic_sample:
                        # 检查样本质量
                        if self._check_sample_quality(synthetic_sample, embeddings):
                            synthetic_samples.append(synthetic_sample)
                            success = True
                            if self.debug and attempt > 0:
                                print(f"【生成成功】在第 {attempt+1} 次尝试后成功")
                            break

                # 如果多次尝试后仍未成功，放宽标准，直接使用最后一个生成的样本
                if not success and synthetic_sample:
                    if self.debug:
                        print(f"【强制接受】在 {max_attempts} 次尝试后未找到高质量样本，强制接受最后一个样本")
                    synthetic_samples.append(synthetic_sample)

        return synthetic_samples

    def _get_sample_embeddings(self, samples):
        """
        Get embeddings for a list of samples.

        Args:
            samples: List of samples

        Returns:
            Tensor of embeddings
        """
        self.encoder.eval()
        self.dropout_layer.eval()

        embeddings = []
        data_loader = get_data_loader(self.config, samples, shuffle=False, batch_size=8)

        with torch.no_grad():
            for step, (labels, _, tokens) in enumerate(data_loader):
                tokens = torch.stack([x.to(self.device) for x in tokens], dim=0)
                reps = self.encoder(tokens)
                outputs, _ = self.dropout_layer(reps)
                embeddings.append(outputs.cpu())

        return torch.cat(embeddings, dim=0)

    def _create_synthetic_sample(self, sample1, sample2, relation_id):
        """
        Create a synthetic sample by combining two existing samples.

        Args:
            sample1: First sample
            sample2: Second sample
            relation_id: ID of the relation

        Returns:
            Synthetic sample or None if creation fails
        """
        try:
            # Extract entity positions
            e11_1 = sample1['tokens'].index(30522)  # [E11] token ID
            e12_1 = sample1['tokens'].index(30523)  # [E12] token ID
            e21_1 = sample1['tokens'].index(30524)  # [E21] token ID
            e22_1 = sample1['tokens'].index(30525)  # [E22] token ID

            e11_2 = sample2['tokens'].index(30522)
            e12_2 = sample2['tokens'].index(30523)
            e21_2 = sample2['tokens'].index(30524)
            e22_2 = sample2['tokens'].index(30525)

            # Create a new sample with head entity from sample1 and tail entity from sample2
            # Start with tokens from sample1
            new_tokens = sample1['tokens'].copy()

            # Replace tail entity with the one from sample2
            tail_entity_2 = sample2['tokens'][e21_2+1:e22_2]

            # Replace the tail entity in new_tokens
            new_tokens = new_tokens[:e21_1+1] + tail_entity_2 + new_tokens[e22_1:]

            # Ensure the token sequence is not too long
            if len(new_tokens) > self.config.max_length:
                new_tokens = new_tokens[:self.config.max_length]

            # Create the synthetic sample
            synthetic_sample = {
                'relation': relation_id,
                'neg_labels': sample1['neg_labels'],  # Keep the negative labels from sample1
                'tokens': new_tokens
            }

            return synthetic_sample

        except (ValueError, IndexError):
            # If there's an error in finding entity markers or other issues
            return None

    def _create_noisy_sample(self, sample, relation_id):
        """
        Create a synthetic sample by adding noise to an existing sample.
        This is used when only one sample is available for a relation.

        Args:
            sample: The original sample
            relation_id: ID of the relation

        Returns:
            Synthetic sample or None if creation fails
        """
        try:
            # Extract entity positions
            e11 = sample['tokens'].index(30522)  # [E11] token ID
            e12 = sample['tokens'].index(30523)  # [E12] token ID
            e21 = sample['tokens'].index(30524)  # [E21] token ID
            e22 = sample['tokens'].index(30525)  # [E22] token ID

            # 选择一种生成方法
            # 1: 实体噪声, 2: 上下文噪声, 3: 实体替换, 4: 上下文替换, 5: 句法变换
            method = random.randint(1, 5)

            if self.debug:
                print(f"【生成方法】使用方法 {method} 生成伪样本")

            # 创建原始tokens的副本
            new_tokens = sample['tokens'].copy()

            if method == 1:
                # 方法1: 实体噪声 - 在实体内部添加轻微噪声
                # 获取头实体和尾实体
                head_entity = new_tokens[e11+1:e12]
                tail_entity = new_tokens[e21+1:e22]

                # 对头实体添加噪声
                if len(head_entity) > 0:
                    # 随机选择是添加、删除还是替换
                    noise_type = random.choice(['add', 'delete', 'replace'])

                    if noise_type == 'add' and len(head_entity) < 5:
                        # 添加一个常见token
                        common_tokens = [1996, 2003, 2001, 1037, 1999]  # 常见词如 the, is, a, in 等
                        pos = random.randint(0, len(head_entity))
                        head_entity.insert(pos, random.choice(common_tokens))

                    elif noise_type == 'delete' and len(head_entity) > 1:
                        # 删除一个token
                        pos = random.randint(0, len(head_entity)-1)
                        head_entity.pop(pos)

                    elif noise_type == 'replace' and len(head_entity) > 0:
                        # 替换一个token
                        pos = random.randint(0, len(head_entity)-1)
                        # 使用相似token替换，确保在有效范围内
                        original_token = head_entity[pos]
                        # BERT词汇表范围通常是0到30000
                        min_token_id = 1000  # 避免特殊token
                        max_token_id = 29000  # 避免接近词汇表边界
                        offset = random.randint(-50, 50)
                        new_token = max(min_token_id, min(max_token_id, original_token + offset))
                        head_entity[pos] = new_token

                # 对尾实体添加噪声，类似头实体
                if len(tail_entity) > 0:
                    noise_type = random.choice(['add', 'delete', 'replace'])

                    if noise_type == 'add' and len(tail_entity) < 5:
                        common_tokens = [1996, 2003, 2001, 1037, 1999]
                        pos = random.randint(0, len(tail_entity))
                        tail_entity.insert(pos, random.choice(common_tokens))

                    elif noise_type == 'delete' and len(tail_entity) > 1:
                        pos = random.randint(0, len(tail_entity)-1)
                        tail_entity.pop(pos)

                    elif noise_type == 'replace' and len(tail_entity) > 0:
                        pos = random.randint(0, len(tail_entity)-1)
                        # 使用相似token替换，确保在有效范围内
                        original_token = tail_entity[pos]
                        # BERT词汇表范围通常是0到30000
                        min_token_id = 1000  # 避免特殊token
                        max_token_id = 29000  # 避免接近词汇表边界
                        offset = random.randint(-50, 50)
                        new_token = max(min_token_id, min(max_token_id, original_token + offset))
                        tail_entity[pos] = new_token

                # 更新tokens，确保保留特殊标记
                new_tokens = new_tokens[:e11+1] + head_entity + new_tokens[e12:e21+1] + tail_entity + new_tokens[e22:]

            elif method == 2:
                # 方法2: 上下文噪声 - 在非实体部分添加、删除或替换token
                # 找到非实体部分
                non_entity_parts = []
                if e11 > 1:  # 句子开头到第一个实体
                    non_entity_parts.append((1, e11))
                if e12 < e21:  # 第一个实体到第二个实体之间
                    non_entity_parts.append((e12, e21))
                if e22 < len(new_tokens)-1:  # 第二个实体到句子结束
                    non_entity_parts.append((e22, len(new_tokens)-1))

                if non_entity_parts:
                    # 随机选择一个非实体部分
                    start, end = random.choice(non_entity_parts)
                    if end - start > 1:
                        # 随机选择操作类型
                        op_type = random.choice(['add', 'delete', 'replace'])

                        if op_type == 'add':
                            # 添加一个常见token
                            common_tokens = [1996, 2003, 2001, 1037, 1999, 2000, 2010, 2005]
                            pos = random.randint(start, end)
                            new_tokens.insert(pos, random.choice(common_tokens))

                            # 更新后续特殊标记的位置
                            if pos <= e11:
                                e11 += 1
                                e12 += 1
                                e21 += 1
                                e22 += 1
                            elif pos <= e12:
                                e12 += 1
                                e21 += 1
                                e22 += 1
                            elif pos <= e21:
                                e21 += 1
                                e22 += 1
                            elif pos <= e22:
                                e22 += 1

                        elif op_type == 'delete':
                            # 删除一个token
                            pos = random.randint(start, end-1)
                            new_tokens.pop(pos)

                            # 更新后续特殊标记的位置
                            if pos < e11:
                                e11 -= 1
                                e12 -= 1
                                e21 -= 1
                                e22 -= 1
                            elif pos < e12:
                                e12 -= 1
                                e21 -= 1
                                e22 -= 1
                            elif pos < e21:
                                e21 -= 1
                                e22 -= 1
                            elif pos < e22:
                                e22 -= 1

                        elif op_type == 'replace':
                            # 替换一个token
                            pos = random.randint(start, end-1)
                            # 使用相似token替换，确保在有效范围内
                            original_token = new_tokens[pos]
                            # BERT词汇表范围通常是0到30000
                            min_token_id = 1000  # 避免特殊token
                            max_token_id = 29000  # 避免接近词汇表边界
                            offset = random.randint(-50, 50)
                            new_token = max(min_token_id, min(max_token_id, original_token + offset))
                            new_tokens[pos] = new_token

            elif method == 3:
                # 方法3: 实体替换 - 使用常见实体替换原有实体
                # 常见实体列表
                common_head_entities = [
                    [2009],  # I
                    [2002],  # he
                    [2016],  # she
                    [1996, 2616],  # the company
                    [1996, 2158],  # the person
                    [1996, 3050],  # the organization
                ]

                common_tail_entities = [
                    [2009],  # I
                    [2002],  # he
                    [2016],  # she
                    [1996, 2616],  # the company
                    [1996, 2158],  # the person
                    [1996, 3050],  # the organization
                ]

                # 随机选择是替换头实体还是尾实体或两者都替换
                replace_type = random.choice(['head', 'tail', 'both'])

                if replace_type in ['head', 'both']:
                    # 替换头实体
                    new_head = random.choice(common_head_entities)
                    new_tokens = new_tokens[:e11+1] + new_head + new_tokens[e12:]

                    # 更新后续特殊标记的位置
                    old_head_len = e12 - (e11+1)
                    new_head_len = len(new_head)
                    diff = new_head_len - old_head_len
                    e12 = e11 + 1 + new_head_len
                    e21 += diff
                    e22 += diff

                if replace_type in ['tail', 'both']:
                    # 替换尾实体
                    new_tail = random.choice(common_tail_entities)
                    new_tokens = new_tokens[:e21+1] + new_tail + new_tokens[e22:]

                    # 更新特殊标记的位置
                    old_tail_len = e22 - (e21+1)
                    new_tail_len = len(new_tail)
                    diff = new_tail_len - old_tail_len
                    e22 = e21 + 1 + new_tail_len

            elif method == 4:
                # 方法4: 上下文替换 - 使用模板替换上下文
                # 常见关系模板
                templates = [
                    [1996, 2158, 2003, 1037, 2364, 1997, 30522, None, 30523, 1999, 30524, None, 30525],  # The person is a member of [E11] X [E12] in [E21] Y [E22]
                    [30522, None, 30523, 2003, 1996, 2364, 1997, 30524, None, 30525],  # [E11] X [E12] is the member of [E21] Y [E22]
                    [30522, None, 30523, 2038, 1999, 30524, None, 30525],  # [E11] X [E12] works in [E21] Y [E22]
                    [30522, None, 30523, 2003, 2013, 30524, None, 30525],  # [E11] X [E12] is from [E21] Y [E22]
                ]

                # 随机选择一个模板
                template = random.choice(templates)

                # 提取原始实体
                head_entity = new_tokens[e11+1:e12]
                tail_entity = new_tokens[e21+1:e22]

                # 创建新的tokens序列
                new_tokens = []
                for token in template:
                    if token == 30522:  # [E11]
                        new_tokens.append(token)
                    elif token == 30523:  # [E12]
                        new_tokens.append(token)
                    elif token == 30524:  # [E21]
                        new_tokens.append(token)
                    elif token == 30525:  # [E22]
                        new_tokens.append(token)
                    elif token is None:
                        # None表示插入实体
                        if len(new_tokens) > 0 and new_tokens[-1] == 30522:
                            # 插入头实体
                            new_tokens.extend(head_entity)
                        elif len(new_tokens) > 0 and new_tokens[-1] == 30524:
                            # 插入尾实体
                            new_tokens.extend(tail_entity)
                    else:
                        new_tokens.append(token)

                # 更新实体位置
                e11 = new_tokens.index(30522)
                e12 = new_tokens.index(30523)
                e21 = new_tokens.index(30524)
                e22 = new_tokens.index(30525)

            elif method == 5:
                # 方法5: 句法变换 - 调整句子结构
                # 找到实体之间的部分
                if e12 < e21 and e21 - e12 > 3:
                    # 有足够的空间进行变换
                    middle_part = new_tokens[e12:e21]

                    # 随机选择变换类型
                    transform_type = random.choice(['reverse', 'shuffle', 'insert'])

                    if transform_type == 'reverse':
                        # 反转中间部分
                        middle_part = middle_part[::-1]

                    elif transform_type == 'shuffle':
                        # 随机打乱中间部分
                        if len(middle_part) > 2:
                            shuffle_part = middle_part[1:-1]
                            random.shuffle(shuffle_part)
                            middle_part = [middle_part[0]] + shuffle_part + [middle_part[-1]]

                    elif transform_type == 'insert':
                        # 在中间插入常见连接词
                        connectors = [
                            [1998],  # and
                            [2030],  # or
                            [2005],  # to
                            [2075],  # as
                            [2008],  # which
                        ]

                        connector = random.choice(connectors)
                        pos = random.randint(1, len(middle_part)-1)
                        middle_part = middle_part[:pos] + connector + middle_part[pos:]

                    # 更新tokens
                    new_tokens = new_tokens[:e12] + middle_part + new_tokens[e21:]

                    # 更新尾实体位置
                    diff = len(middle_part) - (e21 - e12)
                    e21 = e12 + len(middle_part)
                    e22 += diff

            # 确保token序列不会太长
            if len(new_tokens) > self.config.max_length:
                new_tokens = new_tokens[:self.config.max_length]

            # 确保所有特殊标记都存在
            special_tokens = [30522, 30523, 30524, 30525]  # [E11], [E12], [E21], [E22]
            for token in special_tokens:
                if token not in new_tokens:
                    if self.debug:
                        print(f"【样本拒绝】生成的样本缺少特殊标记 {token}，跳过")
                    return None

            # 创建合成样本
            synthetic_sample = {
                'relation': relation_id,
                'neg_labels': sample['neg_labels'],  # 保持原始的负样本标签
                'tokens': new_tokens
            }

            return synthetic_sample

        except (ValueError, IndexError) as e:
            # 如果在查找实体标记或其他问题时出错
            if self.debug:
                print(f"【创建错误】创建噪声样本时出错: {e}")
            return None

    def _check_sample_quality(self, sample, reference_embeddings):
        """
        Check the quality of a synthetic sample by comparing its embedding
        with reference embeddings.

        Args:
            sample: Synthetic sample
            reference_embeddings: Embeddings of real samples for the relation

        Returns:
            Boolean indicating whether the sample is of good quality
        """
        self.encoder.eval()
        self.dropout_layer.eval()

        # Convert sample to batch format
        tokens = torch.tensor(sample['tokens']).unsqueeze(0).to(self.device)

        with torch.no_grad():
            # Get embedding for the synthetic sample
            reps = self.encoder(tokens)
            output, _ = self.dropout_layer(reps)
            sample_embedding = output.cpu()

            # 如果没有参考嵌入，直接返回True
            if reference_embeddings.size(0) == 0:
                return True

            # Calculate cosine similarity with reference embeddings
            similarities = F.cosine_similarity(sample_embedding, reference_embeddings)
            max_similarity = similarities.max().item()

            if self.debug:
                print(f"【质量检查】样本最大相似度: {max_similarity:.4f}, 质量阈值: {self.quality_threshold:.4f}")

            # Check if the sample is too similar to existing samples (avoid duplicates)
            # 进一步放宽重复样本的判断标准
            if max_similarity > 0.99:  # 提高重复判断阈值到0.99
                if self.debug:
                    print(f"【样本拒绝】原因: 与现有样本过于相似 ({max_similarity:.4f} > 0.99)")
                return False

            # 大幅降低质量要求，只要有一定相关性即可
            # 如果样本与任何参考样本的相似度都很低，可能是无意义的样本
            min_threshold = min(0.05, self.quality_threshold)  # 使用更低的阈值
            if max_similarity < min_threshold:
                if self.debug:
                    print(f"【样本拒绝】原因: 质量不足 ({max_similarity:.4f} < {min_threshold:.4f})")
                return False

            if self.debug:
                print(f"【样本接受】相似度 = {max_similarity:.4f} (在 {self.quality_threshold:.2f} 到 0.98 的合理范围内)")
            return True

    def refine_synthetic_data(self, synthetic_data, prev_encoder, prev_dropout_layer, prev_classifier):
        """
        Refine synthetic data using the previous model to ensure quality.

        Args:
            synthetic_data: List of synthetic data samples
            prev_encoder: Previous encoder model
            prev_dropout_layer: Previous dropout layer
            prev_classifier: Previous classifier

        Returns:
            Refined list of synthetic data
        """
        if not synthetic_data or prev_encoder is None:
            return synthetic_data

        refined_data = []

        # 首先验证所有样本是否包含必要的特殊标记
        valid_synthetic_data = []
        for sample in synthetic_data:
            tokens = sample['tokens']
            # 检查是否包含所有必要的特殊标记
            if (30522 in tokens and  # [E11]
                30523 in tokens and  # [E12]
                30524 in tokens and  # [E21]
                30525 in tokens):    # [E22]
                valid_synthetic_data.append(sample)
            elif self.debug:
                print(f"样本被过滤：缺少必要的特殊标记")

        if self.debug:
            valid_rate = len(valid_synthetic_data) / max(1, len(synthetic_data)) * 100
            print(f"【验证结果】有效伪样本: {len(valid_synthetic_data)}/{len(synthetic_data)} (有效率: {valid_rate:.1f}%)")

            # 统计被过滤的原因
            if len(valid_synthetic_data) < len(synthetic_data):
                print(f"【过滤原因】缺少必要的特殊标记，如[E11], [E12], [E21], [E22]")

        if not valid_synthetic_data:
            return []

        prev_encoder.eval()
        prev_dropout_layer.eval()
        prev_classifier.eval()

        # 使用单样本处理，避免批处理问题
        with torch.no_grad():
            for idx, sample in enumerate(valid_synthetic_data):
                try:
                    # 准备单个样本
                    label = torch.tensor([sample['relation']], device=self.device)
                    tokens = torch.tensor(sample['tokens'], device=self.device).unsqueeze(0)

                    # 检查样本中是否包含必要的特殊标记
                    token_list = tokens[0].cpu().numpy()
                    if (30522 not in token_list or  # [E11]
                        30524 not in token_list):   # [E21]
                        continue

                    # Get predictions from previous model
                    reps = prev_encoder(tokens)
                    outputs, _ = prev_dropout_layer(reps)
                    logits = prev_classifier(outputs)

                    # Get predicted labels and probabilities
                    probs = F.softmax(logits, dim=1)
                    _, predicted = torch.max(logits, 1)

                    # 获取正确标签的预测概率
                    label_prob = probs[0][label.item()].item()

                    # 条件1：预测标签与目标标签匹配
                    exact_match = (predicted[0].item() == label.item())

                    # 条件2：目标标签的预测概率超过阈值（即使不是最高的）
                    prob_threshold = 0.05  # 进一步降低阈值，只要有5%的概率即可
                    prob_match = (label_prob >= prob_threshold)

                    # 条件3：目标标签在前N个预测中
                    _, topk_indices = torch.topk(probs[0], 3)  # 忽略topk_values，只使用indices
                    topk_match = label.item() in topk_indices.cpu().numpy()

                    # 满足任一条件即接受样本
                    if exact_match or prob_match or topk_match:
                        if self.debug and not exact_match:
                            print(f"【接受非精确匹配样本】标签={label.item()}, 预测={predicted[0].item()}, 概率={label_prob:.4f}")
                        refined_data.append(sample)

                    # 每处理50个样本输出一次进度
                    if self.debug and (idx + 1) % 50 == 0:
                        print(f"【优化进度】已处理 {idx + 1}/{len(valid_synthetic_data)} 个样本，当前保留 {len(refined_data)} 个")

                except Exception as e:
                    if self.debug:
                        print(f"【处理错误】样本 {idx} 出错: {e}")
                    continue

        if self.debug:
            refined_rate = len(refined_data) / max(1, len(valid_synthetic_data)) * 100
            print(f"\n【优化结果】优化后的伪样本: {len(refined_data)}/{len(valid_synthetic_data)} (保留率: {refined_rate:.1f}%)")
            if len(refined_data) < len(valid_synthetic_data):
                print(f"【优化过滤原因】模型预测的关系与样本标记的关系不匹配")

        return refined_data
