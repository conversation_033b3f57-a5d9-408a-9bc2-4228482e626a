import torch
from transformers import BertTokenizer
from model.bert_encoder import Bert_Encoder

def test_bert_encoder():
    # 定义一个模拟配置
    class Config:
        def __init__(self):
            self.bert_path = "bert-base-uncased"  # 替换为实际BERT模型路径
            self.encoder_output_size = 768  # 选择合适的输出尺寸
            self.drop_out = 0.1  # 设置dropout率
            self.pattern = "entity_marker"  # 选择"standard"或"entity_marker"
            self.vocab_size = 30522  # 替换为实际BERT模型的词汇表大小
            self.marker_size = 2  # 假设两个标记[E11]和[E21]

    # 创建示例输入
    example_input_ids = torch.tensor([
        [101, 102, 103, 30522, 105, 106, 107, 30524, 109, 1010],
        [101, 102, 103, 30524, 105, 106, 107, 30522, 109, 1010],
        [101, 102, 103, 30522, 105, 106, 107, 30524, 109, 1010]
    ]).unsqueeze(0)  # 形状：[1, 3, 10]

    # 初始化Bert_Encoder类
    config = Config()
    bert_encoder = Bert_Encoder(config)

    # 打印编码器的输出尺寸
    print("编码器输出尺寸:", bert_encoder.get_output_size())

    # 使用BERT分词器对示例输入进行分词（仅作演示）
    # 使用BERT分词器对示例输入进行分词（仅作演示）
    tokenizer = BertTokenizer.from_pretrained(config.bert_path)
    example_input_tokens_list = []

    for sample_id in range(example_input_ids.shape[1]):  # 遍历样本维度
        sample_input_ids = example_input_ids[0, sample_id].tolist()  # 提取当前样本的ID列表
        example_input_tokens_list.append(tokenizer.convert_ids_to_tokens(sample_input_ids))  # 转换为token列表并添加到结果列表中

    # 打印分词后的示例输入以供参考
    print("\n分词后的示例输入:")
    for sample_tokens in example_input_tokens_list:
        print(sample_tokens)

    # 对编码器进行前向传播
    output = bert_encoder(example_input_ids)

    # 打印输出形状和输出的前几个元素
    print("\n编码器输出形状:", output.shape)
    print("输出的前几个元素:\n", output[:2])


if __name__ == "__main__":
    test_bert_encoder()