import argparse
import torch
import random
import numpy as np
from config import Config
from model.bert_encoder import Bert_Encoder
from model.dropout_layer import Dropout_Layer
from model.classifier import Softmax_Layer
from ssr import SSR
from transformers import BertTokenizer

def setup_seed(seed):
    """设置随机种子以确保结果可重现"""
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)
    torch.backends.cudnn.deterministic = True

def create_sample_data(config, num_samples=5, num_relations=2):
    """创建示例数据用于测试"""
    tokenizer = BertTokenizer.from_pretrained(config.bert_path,
                                             additional_special_tokens=["[E11]", "[E12]", "[E21]", "[E22]"])

    # 创建示例句子
    sample_sentences = [
        "[E11]苹果[E12]是[E21]水果[E22]的一种。",
        "[E11]北京[E12]是[E21]中国[E22]的首都。",
        "[E11]猫[E12]是一种常见的[E21]宠物[E22]。",
        "[E11]太阳[E12]是[E21]太阳系[E22]的中心。",
        "[E11]水[E12]是[E21]生命[E22]必不可少的物质。"
    ]

    # 创建示例数据
    data = {}
    for rel_id in range(num_relations):
        relation_name = f"relation_{rel_id}"
        data[relation_name] = []

        for i in range(num_samples):
            sentence = sample_sentences[i % len(sample_sentences)]
            # 编码句子
            encoded = tokenizer.encode(sentence, add_special_tokens=True, max_length=config.max_length, padding='max_length', truncation=True)

            # 创建样本
            sample = {
                'relation': rel_id,
                'neg_labels': [j for j in range(num_relations) if j != rel_id],
                'tokens': encoded
            }
            data[relation_name].append(sample)

    # 创建rel2id映射
    rel2id = {f"relation_{i}": i for i in range(num_relations)}

    return data, rel2id

def test_ssr_module():
    """测试SSR模块的功能"""
    # 解析命令行参数
    parser = argparse.ArgumentParser()
    parser.add_argument('--config', default='config.ini', help='配置文件路径')
    args = parser.parse_args()

    # 加载配置
    config = Config(args.config)
    # 强制使用CPU
    config.device = torch.device("cpu")

    # 设置随机种子
    setup_seed(config.seed)

    # 创建模型
    encoder = Bert_Encoder(config=config).to(config.device)
    dropout_layer = Dropout_Layer(config=config).to(config.device)
    classifier = Softmax_Layer(input_size=encoder.output_size, num_class=2).to(config.device)

    # 创建示例数据
    data, rel2id = create_sample_data(config)

    # 初始化SSR模块
    ssr_module = SSR(config, encoder, dropout_layer, classifier)

    # 测试生成伪数据
    print("测试SSR模块生成伪数据...")
    synthetic_data = ssr_module.generate_synthetic_data(list(data.keys()), data, rel2id)

    print(f"生成了 {len(synthetic_data)} 个伪样本")

    # 如果生成了伪样本，打印第一个样本的信息
    if synthetic_data:
        print("\n第一个伪样本信息:")
        sample = synthetic_data[0]
        print(f"关系ID: {sample['relation']}")
        print(f"负样本标签: {sample['neg_labels']}")

        # 解码tokens
        tokenizer = BertTokenizer.from_pretrained(config.bert_path,
                                                 additional_special_tokens=["[E11]", "[E12]", "[E21]", "[E22]"])
        decoded = tokenizer.decode(sample['tokens'], skip_special_tokens=False)
        print(f"解码后的文本: {decoded}")

    # 测试优化伪数据
    print("\n测试SSR模块优化伪数据...")
    refined_data = ssr_module.refine_synthetic_data(synthetic_data, encoder, dropout_layer, classifier)

    print(f"优化后的伪样本数量: {len(refined_data)}")

    return synthetic_data, refined_data

if __name__ == "__main__":
    synthetic_data, refined_data = test_ssr_module()
