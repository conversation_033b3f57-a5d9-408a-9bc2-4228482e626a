import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.optim as optim
import numpy as np
from data_loader import get_data_loader
from ssr import SSR

def train_with_ssr(config, encoder, dropout_layer, classifier, 
                  current_data, history_relations, history_data, rel2id,
                  prev_encoder=None, prev_dropout_layer=None, prev_classifier=None):
    """
    使用SSR增强的训练流程
    
    Args:
        config: 配置对象
        encoder: BERT编码器
        dropout_layer: Dropout层
        classifier: 分类器
        current_data: 当前步骤的训练数据
        history_relations: 历史关系列表
        history_data: 历史关系数据
        rel2id: 关系到ID的映射
        prev_encoder: 前一个编码器模型（可选）
        prev_dropout_layer: 前一个Dropout层（可选）
        prev_classifier: 前一个分类器（可选）
    
    Returns:
        训练后的模型
    """
    # 如果没有历史关系，直接使用当前数据训练
    if not history_relations:
        print("没有历史关系，跳过SSR生成")
        return train_simple_model(config, encoder, dropout_layer, classifier, current_data)
    
    print(f"开始SSR增强训练，历史关系数量: {len(history_relations)}")
    
    # 初始化SSR生成器
    ssr_generator = SSR(config, encoder, dropout_layer, classifier)
    
    # 准备历史关系数据
    prev_relation_data = {}
    for relation in history_relations:
        if relation in history_data:
            prev_relation_data[relation] = history_data[relation]
    
    # 生成伪样本
    print(f"为 {len(history_relations)} 个历史关系生成伪样本...")
    synthetic_samples = ssr_generator.generate_synthetic_data(history_relations, prev_relation_data, rel2id)
    
    # 如果有前一个模型，使用它优化伪样本
    if prev_encoder is not None and prev_dropout_layer is not None and prev_classifier is not None:
        print("使用前一个模型优化伪样本...")
        synthetic_samples = ssr_generator.refine_synthetic_data(
            synthetic_samples, prev_encoder, prev_dropout_layer, prev_classifier
        )
    
    print(f"成功生成 {len(synthetic_samples)} 个伪样本")
    
    # 合并当前数据和伪样本
    combined_data = current_data + synthetic_samples
    print(f"合并后的训练数据大小: {len(combined_data)} (当前数据: {len(current_data)}, 伪样本: {len(synthetic_samples)})")
    
    # 训练模型
    return train_model(config, encoder, dropout_layer, classifier, combined_data)

def train_simple_model(config, encoder, dropout_layer, classifier, training_data):
    """
    使用简单训练流程（无SSR）
    
    Args:
        config: 配置对象
        encoder: BERT编码器
        dropout_layer: Dropout层
        classifier: 分类器
        training_data: 训练数据
    
    Returns:
        训练后的模型
    """
    return train_model(config, encoder, dropout_layer, classifier, training_data)

def train_model(config, encoder, dropout_layer, classifier, training_data):
    """
    通用模型训练函数
    
    Args:
        config: 配置对象
        encoder: BERT编码器
        dropout_layer: Dropout层
        classifier: 分类器
        training_data: 训练数据
    
    Returns:
        训练后的模型
    """
    data_loader = get_data_loader(config, training_data, shuffle=True)
    
    encoder.train()
    dropout_layer.train()
    classifier.train()
    
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam([
        {'params': encoder.parameters(), 'lr': 0.00001},
        {'params': dropout_layer.parameters(), 'lr': 0.00001},
        {'params': classifier.parameters(), 'lr': 0.001}
    ])
    
    for epoch in range(config.epochs):
        losses = []
        for step, (labels, _, tokens) in enumerate(data_loader):
            optimizer.zero_grad()
            
            # 处理不同长度的样本
            try:
                # 尝试使用批处理
                tokens = torch.stack([x.to(config.device) for x in tokens], dim=0)
                batch_processing = True
            except RuntimeError as e:
                if "stack expects each tensor to be equal size" in str(e):
                    # 如果样本长度不同，使用单样本处理
                    print("【批处理警告】检测到不同长度的样本，切换到单样本处理模式")
                    batch_processing = False
                else:
                    # 其他错误，直接抛出
                    raise e
            
            if batch_processing:
                # 批处理模式
                labels = labels.to(config.device)
                reps = encoder(tokens)
                outputs, _ = dropout_layer(reps)
                logits = classifier(outputs)
                loss = criterion(logits, labels)
            else:
                # 单样本处理模式
                loss = 0
                for i in range(len(tokens)):
                    token = tokens[i].unsqueeze(0).to(config.device)
                    label = labels[i].unsqueeze(0).to(config.device)
                    rep = encoder(token)
                    output, _ = dropout_layer(rep)
                    logit = classifier(output)
                    loss += criterion(logit, label)
                loss /= len(tokens)  # 平均损失
            
            loss.backward()
            optimizer.step()
            losses.append(loss.item())
        
        print(f"Epoch {epoch+1}/{config.epochs}, Loss: {np.mean(losses):.4f}")
    
    return encoder, dropout_layer, classifier
