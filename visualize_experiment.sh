#!/bin/bash

# 设置环境变量
export PYTHONIOENCODING=utf-8

# 创建可视化目录
mkdir -p visualizations

# 使用简单可视化脚本
echo "使用简单可视化脚本直接从检查点生成可视化结果..."
python simple_visualization.py --checkpoint_dir checkpoints --output_dir visualizations

echo "可视化完成！结果保存在 visualizations 目录中。"
echo "生成的图表包括："
echo "- learning_curve.png: 学习曲线（准确率和迁移指标）"
echo "- round_comparison.png: 轮次比较图"
echo "- accuracy_heatmap.png: 准确率热力图"
echo "- forgetting_analysis.png: 遗忘分析图"
echo "- summary_report.csv: 摘要统计报告"
