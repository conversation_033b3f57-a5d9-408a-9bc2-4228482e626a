#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import torch
import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import argparse
from collections import defaultdict
import pandas as pd
from matplotlib.ticker import MaxNLocator

def load_checkpoint_data(checkpoint_dir='checkpoints'):
    """
    加载所有检查点的数据

    Args:
        checkpoint_dir: 检查点目录

    Returns:
        包含所有检查点数据的字典
    """
    results = defaultdict(list)

    # 获取所有检查点文件
    checkpoint_files = [f for f in os.listdir(checkpoint_dir) if f.endswith('.pt')]
    checkpoint_files.sort()  # 按文件名排序

    for checkpoint_file in checkpoint_files:
        # 解析文件名以获取轮次和步骤信息
        parts = checkpoint_file.split('_')
        task = parts[1]
        shot = int(parts[2])
        round_num = int(parts[3].replace('round', ''))
        step_num = int(parts[4].replace('step', ''))

        # 加载检查点
        checkpoint_path = os.path.join(checkpoint_dir, checkpoint_file)
        try:
            checkpoint = torch.load(checkpoint_path, map_location=lambda storage, loc: storage)

            # 提取结果数据
            if 'additional_data' in checkpoint and 'results' in checkpoint['additional_data']:
                results_data = checkpoint['additional_data']['results']

                # 存储结果
                results['task'].append(task)
                results['shot'].append(shot)
                results['round'].append(round_num)
                results['step'].append(step_num)

                # 存储性能指标
                if 'result_whole_test' in results_data:
                    results['whole_test_acc'].append(results_data['result_whole_test'])
                else:
                    results['whole_test_acc'].append(None)

                if 'avg_result_cur_test' in results_data:
                    results['avg_cur_test_acc'].append(results_data['avg_result_cur_test'])
                else:
                    results['avg_cur_test_acc'].append(None)

                if 'avg_result_all_test' in results_data:
                    results['avg_all_test_acc'].append(results_data['avg_result_all_test'])
                else:
                    results['avg_all_test_acc'].append(None)

                if 'std_result_all_test' in results_data:
                    results['std_all_test_acc'].append(results_data['std_result_all_test'])
                else:
                    results['std_all_test_acc'].append(None)

                if 'bwt_whole' in results_data:
                    results['bwt'].append(results_data['bwt_whole'])
                else:
                    results['bwt'].append(None)

                if 'fwt_whole' in results_data:
                    results['fwt'].append(results_data['fwt_whole'])
                else:
                    results['fwt'].append(None)

                # 如果有详细的每个关系的结果，也存储它们
                if 'detailed_results' in results_data:
                    for rel_id, rel_acc in results_data['detailed_results'].items():
                        results[f'rel_{rel_id}_acc'].append(rel_acc)
            else:
                print(f"警告: 检查点 {checkpoint_file} 中没有找到结果数据")
        except Exception as e:
            print(f"加载检查点 {checkpoint_file} 时出错: {e}")

    return results

def plot_overall_performance(results, output_dir='visualizations'):
    """绘制整体性能图表"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 创建DataFrame
    df = pd.DataFrame({
        'Round': results['round'],
        'Step': results['step'],
        'Whole Test Accuracy': results['whole_test_acc'],
        'Current Test Accuracy': results['avg_cur_test_acc'],
        'All Test Accuracy': results['avg_all_test_acc'],
        'BWT': results['bwt'],
        'FWT': results['fwt']
    })

    # 设置图表样式
    sns.set(style="whitegrid")
    plt.figure(figsize=(12, 8))

    # 绘制准确率曲线
    plt.subplot(2, 1, 1)
    # 创建一个新的列，表示轮次和步骤的组合
    df['Position'] = df['Round'] + df['Step'] / 10
    for metric in ['Whole Test Accuracy', 'Current Test Accuracy', 'All Test Accuracy']:
        if metric in df.columns:
            sns.lineplot(x='Position', y=metric, data=df, marker='o', label=metric)

    plt.title('Accuracy Metrics Over Training')
    plt.xlabel('Training Progress (Round.Step)')
    plt.ylabel('Accuracy')
    plt.legend()

    # 设置x轴刻度为整数
    ax = plt.gca()
    ax.xaxis.set_major_locator(MaxNLocator(integer=True))

    # 绘制BWT和FWT曲线
    plt.subplot(2, 1, 2)
    for metric in ['BWT', 'FWT']:
        sns.lineplot(x='Position', y=metric, data=df, marker='o', label=metric)

    plt.title('Transfer Metrics Over Training')
    plt.xlabel('Training Progress (Round.Step)')
    plt.ylabel('Transfer Score')
    plt.legend()

    # 设置x轴刻度为整数
    ax = plt.gca()
    ax.xaxis.set_major_locator(MaxNLocator(integer=True))

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'overall_performance.png'))
    plt.close()

def plot_per_round_performance(results, output_dir='visualizations'):
    """绘制每轮性能图表"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 创建DataFrame
    df = pd.DataFrame({
        'Round': results['round'],
        'Step': results['step'],
        'Whole Test Accuracy': results['whole_test_acc']
    })

    # 按轮次分组
    rounds = sorted(set(results['round']))

    # 设置图表样式
    sns.set(style="whitegrid")
    plt.figure(figsize=(12, 8))

    for r in rounds:
        round_data = df[df['Round'] == r]
        plt.plot(round_data['Step'], round_data['Whole Test Accuracy'],
                 marker='o', label=f'Round {r}')

    plt.title('Whole Test Accuracy by Round')
    plt.xlabel('Step')
    plt.ylabel('Accuracy')
    plt.legend()
    plt.grid(True)

    # 设置x轴刻度为整数
    ax = plt.gca()
    ax.xaxis.set_major_locator(MaxNLocator(integer=True))

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'per_round_performance.png'))
    plt.close()

def plot_heatmap(results, output_dir='visualizations'):
    """绘制热力图"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 创建DataFrame
    df = pd.DataFrame({
        'Round': results['round'],
        'Step': results['step'],
        'Whole Test Accuracy': results['whole_test_acc']
    })

    # 创建轮次-步骤网格
    pivot_table = df.pivot_table(index='Round', columns='Step',
                                 values='Whole Test Accuracy', aggfunc='mean')

    # 设置图表样式
    plt.figure(figsize=(10, 8))
    sns.heatmap(pivot_table, annot=True, cmap='YlGnBu', fmt='.3f', linewidths=.5)

    plt.title('Whole Test Accuracy Heatmap (Round vs Step)')
    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'accuracy_heatmap.png'))
    plt.close()

def plot_forgetting_analysis(results, output_dir='visualizations'):
    """分析和可视化遗忘现象"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 创建DataFrame
    df = pd.DataFrame({
        'Round': results['round'],
        'Step': results['step'],
        'BWT': results['bwt']
    })

    # 设置图表样式
    plt.figure(figsize=(12, 6))

    # 按步骤分组，查看每个步骤的BWT变化
    steps = sorted(set(results['step']))

    for s in steps:
        step_data = df[df['Step'] == s]
        plt.plot(step_data['Round'], step_data['BWT'],
                 marker='o', label=f'Step {s}')

    plt.title('Backward Transfer (BWT) by Round for Each Step')
    plt.xlabel('Round')
    plt.ylabel('BWT Score')
    plt.legend()
    plt.grid(True)

    # 设置x轴刻度为整数
    ax = plt.gca()
    ax.xaxis.set_major_locator(MaxNLocator(integer=True))

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, 'forgetting_analysis.png'))
    plt.close()

def main():
    parser = argparse.ArgumentParser(description='可视化SCKD实验结果')
    parser.add_argument('--checkpoint_dir', type=str, default='checkpoints',
                        help='检查点目录路径')
    parser.add_argument('--output_dir', type=str, default='visualizations',
                        help='可视化输出目录路径')
    args = parser.parse_args()

    print(f"从 {args.checkpoint_dir} 加载检查点数据...")
    results = load_checkpoint_data(args.checkpoint_dir)

    if not results['round']:
        print("没有找到有效的结果数据。请确保检查点包含结果信息。")
        return

    print(f"生成可视化结果到 {args.output_dir}...")
    plot_overall_performance(results, args.output_dir)
    plot_per_round_performance(results, args.output_dir)
    plot_heatmap(results, args.output_dir)
    plot_forgetting_analysis(results, args.output_dir)

    print("可视化完成！")

if __name__ == "__main__":
    main()
